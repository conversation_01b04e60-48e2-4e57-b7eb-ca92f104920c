# PD Configuration.

name = "pd"
data-dir = "default.pd"

client-urls = "http://127.0.0.1:2379"
## if not set, use ${client-urls}
advertise-client-urls = ""

peer-urls = "http://127.0.0.1:2380"
## if not set, use ${peer-urls}
advertise-peer-urls = ""

initial-cluster = "pd=http://127.0.0.1:2380"
initial-cluster-state = "new"

lease = 3
tso-save-interval = "3s"

[security]
## Path of file that contains list of trusted SSL CAs. if set, following four settings shouldn't be empty
cacert-path = ""
## Path of file that contains X509 certificate in PEM format.
cert-path = ""
## Path of file that contains X509 key in PEM format.
key-path = ""

[log]
level = "info"

## log format, one of json, text, console
# format = "text"

## disable automatic timestamps in output
# disable-timestamp = false

# file logging
[log.file]
# filename = ""
## max log file size in MB
# max-size = 300
## max log file keep days
# max-days = 28
## maximum number of old log files to retain
# max-backups = 7
## rotate log by day
# log-rotate = true

[schedule]
split-merge-interval = "1h"
max-snapshot-count = 3
max-pending-peer-count = 16
max-store-down-time = "30m"
leader-schedule-limit = 4
region-schedule-limit = 2048
replica-schedule-limit = 64
## There are some strategics supported: ["count", "size"], default: "count"
# leader-schedule-strategy = "count" 
## When the score difference between the leader or Region of the two stores is 
## less than specified multiple times of the Region size, it is considered in balance by PD.
## If it equals 0.0, PD will automatically adjust it.
# tolerant-size-ratio = 0.0

## This three parameters control the merge scheduler behavior.
## If it is true, it means a region can only be merged into the next region of it.
# enable-one-way-merge = false
## There are some strategics supported: ["table", "raw", "txn"], default: "table"
# merge-strategy = "table"
## If it is true, it means two region within different tables can be merged.
## This option only works when merge strategy is "table".
# enable-cross-table-merge = false

## customized schedulers, the format is as below
## if empty, it will use balance-leader, balance-region as default
# [[schedule.schedulers]]
# type = "evict-leader"
# args = ["1"]

[replication]
## The number of replicas for each region.
max-replicas = 3
