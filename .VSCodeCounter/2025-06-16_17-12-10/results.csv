"filename", "language", "Markdown", "Go", "<PERSON>AM<PERSON>", "Shell Script", "Go Checksum File", "Makefile", "Go Module File", "comment", "blank", "total"
"/Users/<USER>/WorkSpace/tinykv/.github/workflows/go.yml", "YAML", 0, 0, 16, 0, 0, 0, 0, 0, 5, 21
"/Users/<USER>/WorkSpace/tinykv/Makefile", "Makefile", 0, 0, 0, 0, 0, 103, 0, 2, 29, 134
"/Users/<USER>/WorkSpace/tinykv/README.md", "Markdown", 74, 0, 0, 0, 0, 0, 0, 0, 36, 110
"/Users/<USER>/WorkSpace/tinykv/doc/project1-StandaloneKV.md", "Markdown", 30, 0, 0, 0, 0, 0, 0, 0, 18, 48
"/Users/<USER>/WorkSpace/tinykv/doc/project2-RaftKV.md", "Markdown", 152, 0, 0, 0, 0, 0, 0, 0, 81, 233
"/Users/<USER>/WorkSpace/tinykv/doc/project3-MultiRaftKV.md", "Markdown", 111, 0, 0, 0, 0, 0, 0, 0, 77, 188
"/Users/<USER>/WorkSpace/tinykv/doc/project4-Transaction.md", "Markdown", 50, 0, 0, 0, 0, 0, 0, 0, 39, 89
"/Users/<USER>/WorkSpace/tinykv/doc/reading_list.md", "Markdown", 119, 0, 0, 0, 0, 0, 0, 0, 1, 120
"/Users/<USER>/WorkSpace/tinykv/go.mod", "Go Module File", 0, 0, 0, 0, 0, 0, 34, 0, 4, 38
"/Users/<USER>/WorkSpace/tinykv/go.sum", "Go Checksum File", 0, 0, 0, 0, 438, 0, 0, 0, 1, 439
"/Users/<USER>/WorkSpace/tinykv/kv/config/config.go", "Go", 0, 84, 0, 0, 0, 0, 0, 10, 17, 111
"/Users/<USER>/WorkSpace/tinykv/kv/coprocessor/analyze.go", "Go", 0, 269, 0, 0, 0, 0, 0, 14, 23, 306
"/Users/<USER>/WorkSpace/tinykv/kv/coprocessor/closure_exec.go", "Go", 0, 658, 0, 0, 0, 0, 0, 15, 67, 740
"/Users/<USER>/WorkSpace/tinykv/kv/coprocessor/cop_handler.go", "Go", 0, 284, 0, 0, 0, 0, 0, 23, 34, 341
"/Users/<USER>/WorkSpace/tinykv/kv/coprocessor/rowcodec/common.go", "Go", 0, 194, 0, 0, 0, 0, 0, 7, 20, 221
"/Users/<USER>/WorkSpace/tinykv/kv/coprocessor/rowcodec/decoder.go", "Go", 0, 187, 0, 0, 0, 0, 0, 13, 10, 210
"/Users/<USER>/WorkSpace/tinykv/kv/coprocessor/rowcodec/encoder.go", "Go", 0, 262, 0, 0, 0, 0, 0, 9, 33, 304
"/Users/<USER>/WorkSpace/tinykv/kv/coprocessor/topn.go", "Go", 0, 95, 0, 0, 0, 0, 0, 21, 24, 140
"/Users/<USER>/WorkSpace/tinykv/kv/main.go", "Go", 0, 90, 0, 0, 0, 0, 0, 0, 11, 101
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/bootstrap.go", "Go", 0, 142, 0, 0, 0, 0, 0, 1, 12, 155
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/bootstrap_test.go", "Go", 0, 33, 0, 0, 0, 0, 0, 0, 6, 39
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/cmd_resp.go", "Go", 0, 53, 0, 0, 0, 0, 0, 0, 10, 63
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/message/callback.go", "Go", 0, 39, 0, 0, 0, 0, 0, 0, 8, 47
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/message/msg.go", "Go", 0, 43, 0, 0, 0, 0, 0, 17, 11, 71
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/message/raft_router.go", "Go", 0, 10, 0, 0, 0, 0, 0, 0, 3, 13
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/meta/keys.go", "Go", 0, 94, 0, 0, 0, 0, 0, 10, 20, 124
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/meta/values.go", "Go", 0, 87, 0, 0, 0, 0, 0, 3, 10, 100
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/node.go", "Go", 0, 187, 0, 0, 0, 0, 0, 0, 26, 213
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/peer.go", "Go", 0, 279, 0, 0, 0, 0, 0, 64, 50, 393
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/peer_msg_handler.go", "Go", 0, 474, 0, 0, 0, 0, 0, 54, 46, 574
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/peer_storage.go", "Go", 0, 286, 0, 0, 0, 0, 0, 28, 34, 348
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/peer_storage_test.go", "Go", 0, 228, 0, 0, 0, 0, 0, 4, 16, 248
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/raft_worker.go", "Go", 0, 57, 0, 0, 0, 0, 0, 7, 9, 73
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/raftstore.go", "Go", 0, 273, 0, 0, 0, 0, 0, 15, 27, 315
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/router.go", "Go", 0, 84, 0, 0, 0, 0, 0, 2, 19, 105
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/runner/raftlog_gc.go", "Go", 0, 78, 0, 0, 0, 0, 0, 2, 11, 91
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/runner/region_task.go", "Go", 0, 170, 0, 0, 0, 0, 0, 12, 28, 210
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/runner/runner_test.go", "Go", 0, 241, 0, 0, 0, 0, 0, 7, 31, 279
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/runner/scheduler_task.go", "Go", 0, 133, 0, 0, 0, 0, 0, 0, 20, 153
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/runner/split_checker.go", "Go", 0, 114, 0, 0, 0, 0, 0, 6, 15, 135
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/scheduler_client/client.go", "Go", 0, 478, 0, 0, 0, 0, 0, 16, 62, 556
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/snap/snap.go", "Go", 0, 696, 0, 0, 0, 0, 0, 31, 60, 787
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/snap/snap_builder.go", "Go", 0, 53, 0, 0, 0, 0, 0, 1, 7, 61
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/snap/snap_manager.go", "Go", 0, 320, 0, 0, 0, 0, 0, 1, 25, 346
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/snap/snap_test.go", "Go", 0, 198, 0, 0, 0, 0, 0, 12, 32, 242
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/store_worker.go", "Go", 0, 271, 0, 0, 0, 0, 0, 21, 25, 317
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/ticker.go", "Go", 0, 110, 0, 0, 0, 0, 0, 3, 18, 131
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/util/error.go", "Go", 0, 66, 0, 0, 0, 0, 0, 0, 16, 82
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/util/error_test.go", "Go", 0, 38, 0, 0, 0, 0, 0, 0, 10, 48
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/util/test_util.go", "Go", 0, 35, 0, 0, 0, 0, 0, 0, 4, 39
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/util/util.go", "Go", 0, 150, 0, 0, 0, 0, 0, 29, 29, 208
"/Users/<USER>/WorkSpace/tinykv/kv/raftstore/util/util_test.go", "Go", 0, 165, 0, 0, 0, 0, 0, 7, 22, 194
"/Users/<USER>/WorkSpace/tinykv/kv/server/raw_api.go", "Go", 0, 17, 0, 0, 0, 0, 0, 13, 7, 37
"/Users/<USER>/WorkSpace/tinykv/kv/server/server.go", "Go", 0, 69, 0, 0, 0, 0, 0, 17, 19, 105
"/Users/<USER>/WorkSpace/tinykv/kv/server/server_test.go", "Go", 0, 294, 0, 0, 0, 0, 0, 0, 56, 350
"/Users/<USER>/WorkSpace/tinykv/kv/storage/mem_storage.go", "Go", 0, 215, 0, 0, 0, 0, 0, 4, 33, 252
"/Users/<USER>/WorkSpace/tinykv/kv/storage/modify.go", "Go", 0, 37, 0, 0, 0, 0, 0, 1, 8, 46
"/Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/raft_client.go", "Go", 0, 115, 0, 0, 0, 0, 0, 1, 15, 131
"/Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/raft_server.go", "Go", 0, 190, 0, 0, 0, 0, 0, 3, 27, 220
"/Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/region_reader.go", "Go", 0, 73, 0, 0, 0, 0, 0, 2, 16, 91
"/Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/resolver.go", "Go", 0, 55, 0, 0, 0, 0, 0, 2, 11, 68
"/Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/snap_runner.go", "Go", 0, 161, 0, 0, 0, 0, 0, 0, 22, 183
"/Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/transport.go", "Go", 0, 83, 0, 0, 0, 0, 0, 1, 12, 96
"/Users/<USER>/WorkSpace/tinykv/kv/storage/standalone_storage/standalone_storage.go", "Go", 0, 23, 0, 0, 0, 0, 0, 8, 8, 39
"/Users/<USER>/WorkSpace/tinykv/kv/storage/storage.go", "Go", 0, 16, 0, 0, 0, 0, 0, 3, 4, 23
"/Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/cluster.go", "Go", 0, 425, 0, 0, 0, 0, 0, 4, 45, 474
"/Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/cluster_test.go", "Go", 0, 20, 0, 0, 0, 0, 0, 0, 3, 23
"/Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/filter.go", "Go", 0, 36, 0, 0, 0, 0, 0, 0, 10, 46
"/Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/node.go", "Go", 0, 178, 0, 0, 0, 0, 0, 0, 41, 219
"/Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/scheduler.go", "Go", 0, 475, 0, 0, 0, 0, 0, 16, 78, 569
"/Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/test_test.go", "Go", 0, 550, 0, 0, 0, 0, 0, 95, 97, 742
"/Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/utils.go", "Go", 0, 119, 0, 0, 0, 0, 0, 0, 19, 138
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/commands4b_test.go", "Go", 0, 488, 0, 0, 0, 0, 0, 37, 72, 597
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/commands4c_test.go", "Go", 0, 396, 0, 0, 0, 0, 0, 27, 62, 485
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/commands_test.go", "Go", 0, 172, 0, 0, 0, 0, 0, 16, 24, 212
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/doc.go", "Go", 0, 1, 0, 0, 0, 0, 0, 38, 2, 41
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/latches/latches.go", "Go", 0, 57, 0, 0, 0, 0, 0, 29, 14, 100
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/latches/latches_test.go", "Go", 0, 22, 0, 0, 0, 0, 0, 3, 6, 31
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/mvcc/lock.go", "Go", 0, 81, 0, 0, 0, 0, 0, 4, 14, 99
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/mvcc/scanner.go", "Go", 0, 11, 0, 0, 0, 0, 0, 9, 5, 25
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/mvcc/transaction.go", "Go", 0, 73, 0, 0, 0, 0, 0, 32, 21, 126
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/mvcc/transaction_test.go", "Go", 0, 246, 0, 0, 0, 0, 0, 7, 49, 302
"/Users/<USER>/WorkSpace/tinykv/kv/transaction/mvcc/write.go", "Go", 0, 55, 0, 0, 0, 0, 0, 3, 12, 70
"/Users/<USER>/WorkSpace/tinykv/kv/util/codec/codec.go", "Go", 0, 56, 0, 0, 0, 0, 0, 17, 12, 85
"/Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/cf_iterator.go", "Go", 0, 87, 0, 0, 0, 0, 0, 17, 26, 130
"/Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/doc.go", "Go", 0, 1, 0, 0, 0, 0, 0, 15, 2, 18
"/Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/engine_util_test.go", "Go", 0, 84, 0, 0, 0, 0, 0, 0, 10, 94
"/Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/engines.go", "Go", 0, 66, 0, 0, 0, 0, 0, 8, 10, 84
"/Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/util.go", "Go", 0, 96, 0, 0, 0, 0, 0, 0, 15, 111
"/Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/write_batch.go", "Go", 0, 96, 0, 0, 0, 0, 0, 0, 15, 111
"/Users/<USER>/WorkSpace/tinykv/kv/util/file.go", "Go", 0, 49, 0, 0, 0, 0, 0, 1, 8, 58
"/Users/<USER>/WorkSpace/tinykv/kv/util/worker/worker.go", "Go", 0, 49, 0, 0, 0, 0, 0, 0, 12, 61
"/Users/<USER>/WorkSpace/tinykv/log/log.go", "Go", 0, 223, 0, 0, 0, 0, 0, 6, 54, 283
"/Users/<USER>/WorkSpace/tinykv/proto/generate_go.sh", "Shell Script", 0, 0, 0, 55, 0, 0, 0, 3, 14, 72
"/Users/<USER>/WorkSpace/tinykv/proto/pkg/coprocessor/coprocessor.pb.go", "Go", 0, 1086, 0, 0, 0, 0, 0, 9, 46, 1141
"/Users/<USER>/WorkSpace/tinykv/proto/pkg/eraftpb/eraftpb.pb.go", "Go", 0, 2144, 0, 0, 0, 0, 0, 43, 97, 2284
"/Users/<USER>/WorkSpace/tinykv/proto/pkg/errorpb/errorpb.pb.go", "Go", 0, 1767, 0, 0, 0, 0, 0, 8, 78, 1853
"/Users/<USER>/WorkSpace/tinykv/proto/pkg/kvrpcpb/kvrpcpb.pb.go", "Go", 0, 7685, 0, 0, 0, 0, 0, 62, 308, 8055
"/Users/<USER>/WorkSpace/tinykv/proto/pkg/metapb/metapb.pb.go", "Go", 0, 1318, 0, 0, 0, 0, 0, 14, 64, 1396
"/Users/<USER>/WorkSpace/tinykv/proto/pkg/raft_cmdpb/raft_cmdpb.pb.go", "Go", 0, 5572, 0, 0, 0, 0, 0, 18, 240, 5830
"/Users/<USER>/WorkSpace/tinykv/proto/pkg/raft_serverpb/raft_serverpb.pb.go", "Go", 0, 2964, 0, 0, 0, 0, 0, 24, 130, 3118
"/Users/<USER>/WorkSpace/tinykv/proto/pkg/schedulerpb/schedulerpb.pb.go", "Go", 0, 13305, 0, 0, 0, 0, 0, 65, 586, 13956
"/Users/<USER>/WorkSpace/tinykv/proto/pkg/tinykvpb/tinykvpb.pb.go", "Go", 0, 559, 0, 0, 0, 0, 0, 21, 68, 648
"/Users/<USER>/WorkSpace/tinykv/proto/tools/Makefile", "Makefile", 0, 0, 0, 0, 0, 5, 0, 0, 2, 7
"/Users/<USER>/WorkSpace/tinykv/proto/tools/go.mod", "Go Module File", 0, 0, 0, 0, 0, 0, 6, 0, 3, 9
"/Users/<USER>/WorkSpace/tinykv/proto/tools/go.sum", "Go Checksum File", 0, 0, 0, 0, 34, 0, 0, 0, 1, 35
"/Users/<USER>/WorkSpace/tinykv/proto/tools/mod_guard.go", "Go", 0, 5, 0, 0, 0, 0, 0, 1, 3, 9
"/Users/<USER>/WorkSpace/tinykv/raft/doc.go", "Go", 0, 1, 0, 0, 0, 0, 0, 266, 2, 269
"/Users/<USER>/WorkSpace/tinykv/raft/log.go", "Go", 0, 30, 0, 0, 0, 0, 0, 53, 17, 100
"/Users/<USER>/WorkSpace/tinykv/raft/raft.go", "Go", 0, 101, 0, 0, 0, 0, 0, 95, 44, 240
"/Users/<USER>/WorkSpace/tinykv/raft/raft_paper_test.go", "Go", 0, 717, 0, 0, 0, 0, 0, 138, 74, 929
"/Users/<USER>/WorkSpace/tinykv/raft/raft_test.go", "Go", 0, 1274, 0, 0, 0, 0, 0, 210, 243, 1727
"/Users/<USER>/WorkSpace/tinykv/raft/rawnode.go", "Go", 0, 94, 0, 0, 0, 0, 0, 57, 26, 177
"/Users/<USER>/WorkSpace/tinykv/raft/rawnode_test.go", "Go", 0, 200, 0, 0, 0, 0, 0, 25, 28, 253
"/Users/<USER>/WorkSpace/tinykv/raft/storage.go", "Go", 0, 164, 0, 0, 0, 0, 0, 75, 35, 274
"/Users/<USER>/WorkSpace/tinykv/raft/util.go", "Go", 0, 96, 0, 0, 0, 0, 0, 16, 18, 130
"/Users/<USER>/WorkSpace/tinykv/scheduler/client/client.go", "Go", 0, 626, 0, 0, 0, 0, 0, 60, 97, 783
"/Users/<USER>/WorkSpace/tinykv/scheduler/client/client_test.go", "Go", 0, 352, 0, 0, 0, 0, 0, 26, 43, 421
"/Users/<USER>/WorkSpace/tinykv/scheduler/main.go", "Go", 0, 85, 0, 0, 0, 0, 0, 16, 22, 123
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/apiutil/apiutil.go", "Go", 0, 11, 0, 0, 0, 0, 0, 14, 5, 30
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/btree/.travis.yml", "YAML", 0, 0, 1, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/btree/README.md", "Markdown", 8, 0, 0, 0, 0, 0, 0, 0, 5, 13
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/btree/btree.go", "Go", 0, 729, 0, 0, 0, 0, 0, 270, 90, 1089
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/btree/btree_mem.go", "Go", 0, 55, 0, 0, 0, 0, 0, 15, 7, 77
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/btree/btree_test.go", "Go", 0, 782, 0, 0, 0, 0, 0, 38, 47, 867
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/cache/cache.go", "Go", 0, 13, 0, 0, 0, 0, 0, 20, 4, 37
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/cache/cache_test.go", "Go", 0, 50, 0, 0, 0, 0, 0, 12, 21, 83
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/cache/ttl.go", "Go", 0, 103, 0, 0, 0, 0, 0, 23, 27, 153
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/codec/codec.go", "Go", 0, 103, 0, 0, 0, 0, 0, 38, 23, 164
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/codec/codec_test.go", "Go", 0, 30, 0, 0, 0, 0, 0, 12, 13, 55
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/etcdutil/etcdutil.go", "Go", 0, 99, 0, 0, 0, 0, 0, 24, 19, 142
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/etcdutil/etcdutil_test.go", "Go", 0, 76, 0, 0, 0, 0, 0, 15, 20, 111
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/grpcutil/grpcutil.go", "Go", 0, 45, 0, 0, 0, 0, 0, 16, 9, 70
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/logutil/log.go", "Go", 0, 211, 0, 0, 0, 0, 0, 51, 35, 297
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/logutil/log_test.go", "Go", 0, 79, 0, 0, 0, 0, 0, 14, 19, 112
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/mock/mockcluster/mockcluster.go", "Go", 0, 267, 0, 0, 0, 0, 0, 46, 41, 354
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/mock/mockhbstream/mockhbstream.go", "Go", 0, 67, 0, 0, 0, 0, 0, 22, 15, 104
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/mock/mockid/mockid.go", "Go", 0, 11, 0, 0, 0, 0, 0, 15, 6, 32
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/mock/mockoption/mockoption.go", "Go", 0, 63, 0, 0, 0, 0, 0, 23, 14, 100
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/slice/slice.go", "Go", 0, 20, 0, 0, 0, 0, 0, 15, 6, 41
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/slice/slice_test.go", "Go", 0, 31, 0, 0, 0, 0, 0, 12, 9, 52
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/tempurl/tempurl.go", "Go", 0, 41, 0, 0, 0, 0, 0, 13, 8, 62
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/testutil/operator_check.go", "Go", 0, 48, 0, 0, 0, 0, 0, 19, 8, 75
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/testutil/testutil.go", "Go", 0, 38, 0, 0, 0, 0, 0, 19, 11, 68
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/tsoutil/tso.go", "Go", 0, 12, 0, 0, 0, 0, 0, 13, 5, 30
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/convension.go", "Go", 0, 16, 0, 0, 0, 0, 0, 14, 7, 37
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/duration.go", "Go", 0, 33, 0, 0, 0, 0, 0, 17, 9, 59
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/duration_test.go", "Go", 0, 26, 0, 0, 0, 0, 0, 12, 12, 50
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/size.go", "Go", 0, 30, 0, 0, 0, 0, 0, 16, 8, 54
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/size_test.go", "Go", 0, 24, 0, 0, 0, 0, 0, 12, 10, 46
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/string_slice.go", "Go", 0, 22, 0, 0, 0, 0, 0, 15, 7, 44
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/string_slice_test.go", "Go", 0, 27, 0, 0, 0, 0, 0, 12, 10, 49
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/time.go", "Go", 0, 13, 0, 0, 0, 0, 0, 15, 7, 35
"/Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/time_test.go", "Go", 0, 30, 0, 0, 0, 0, 0, 12, 8, 50
"/Users/<USER>/WorkSpace/tinykv/scheduler/scripts/build-api.sh", "Shell Script", 0, 0, 0, 3, 0, 0, 0, 2, 4, 9
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/cluster.go", "Go", 0, 637, 0, 0, 0, 0, 0, 92, 131, 860
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/cluster_test.go", "Go", 0, 905, 0, 0, 0, 0, 0, 68, 182, 1155
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/cluster_worker.go", "Go", 0, 109, 0, 0, 0, 0, 0, 16, 22, 147
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/cluster_worker_test.go", "Go", 0, 94, 0, 0, 0, 0, 0, 12, 16, 122
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/config/config.go", "Go", 0, 466, 0, 0, 0, 0, 0, 80, 110, 656
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/config/option.go", "Go", 0, 120, 0, 0, 0, 0, 0, 37, 27, 184
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/coordinator.go", "Go", 0, 292, 0, 0, 0, 0, 0, 28, 48, 368
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/coordinator_test.go", "Go", 0, 643, 0, 0, 0, 0, 0, 52, 109, 804
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/basic_cluster.go", "Go", 0, 247, 0, 0, 0, 0, 0, 57, 50, 354
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/errors.go", "Go", 0, 26, 0, 0, 0, 0, 0, 23, 15, 64
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/kind.go", "Go", 0, 30, 0, 0, 0, 0, 0, 19, 9, 58
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/region.go", "Go", 0, 614, 0, 0, 0, 0, 0, 103, 107, 824
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/region_option.go", "Go", 0, 84, 0, 0, 0, 0, 0, 27, 18, 129
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/region_test.go", "Go", 0, 150, 0, 0, 0, 0, 0, 18, 25, 193
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/region_tree.go", "Go", 0, 163, 0, 0, 0, 0, 0, 35, 34, 232
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/region_tree_test.go", "Go", 0, 295, 0, 0, 0, 0, 0, 23, 53, 371
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/storage.go", "Go", 0, 177, 0, 0, 0, 0, 0, 30, 33, 240
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/storage_test.go", "Go", 0, 86, 0, 0, 0, 0, 0, 12, 20, 118
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/store.go", "Go", 0, 327, 0, 0, 0, 0, 0, 77, 68, 472
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/store_option.go", "Go", 0, 82, 0, 0, 0, 0, 0, 27, 19, 128
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/store_test.go", "Go", 0, 38, 0, 0, 0, 0, 0, 12, 7, 57
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/core/test_util.go", "Go", 0, 89, 0, 0, 0, 0, 0, 17, 9, 115
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/grpc_service.go", "Go", 0, 541, 0, 0, 0, 0, 0, 72, 96, 709
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/heartbeat_streams.go", "Go", 0, 139, 0, 0, 0, 0, 0, 12, 21, 172
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/id/id.go", "Go", 0, 78, 0, 0, 0, 0, 0, 18, 21, 117
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/kv/etcd_kv.go", "Go", 0, 123, 0, 0, 0, 0, 0, 21, 23, 167
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/kv/etcd_kv_test.go", "Go", 0, 87, 0, 0, 0, 0, 0, 13, 19, 119
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/kv/kv.go", "Go", 0, 7, 0, 0, 0, 0, 0, 13, 3, 23
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/kv/mem_kv.go", "Go", 0, 53, 0, 0, 0, 0, 0, 13, 13, 79
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/member/leader.go", "Go", 0, 299, 0, 0, 0, 0, 0, 57, 45, 401
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/member/lease.go", "Go", 0, 101, 0, 0, 0, 0, 0, 22, 18, 141
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/checker/replica_checker.go", "Go", 0, 153, 0, 0, 0, 0, 0, 28, 24, 205
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/checker_controller.go", "Go", 0, 31, 0, 0, 0, 0, 0, 18, 7, 56
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/filter/filters.go", "Go", 0, 162, 0, 0, 0, 0, 0, 34, 39, 235
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator/operator.go", "Go", 0, 313, 0, 0, 0, 0, 0, 69, 53, 435
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator/operator_kind.go", "Go", 0, 60, 0, 0, 0, 0, 0, 15, 11, 86
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator/operator_test.go", "Go", 0, 144, 0, 0, 0, 0, 0, 15, 19, 178
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator_controller.go", "Go", 0, 274, 0, 0, 0, 0, 0, 47, 43, 364
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator_controller_test.go", "Go", 0, 98, 0, 0, 0, 0, 0, 18, 19, 135
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator_queue.go", "Go", 0, 31, 0, 0, 0, 0, 0, 12, 11, 54
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/opt/opts.go", "Go", 0, 20, 0, 0, 0, 0, 0, 17, 10, 47
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/scheduler.go", "Go", 0, 93, 0, 0, 0, 0, 0, 28, 19, 140
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/selector/selector.go", "Go", 0, 89, 0, 0, 0, 0, 0, 27, 14, 130
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/selector/selector_test.go", "Go", 0, 26, 0, 0, 0, 0, 0, 12, 11, 49
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/test_util.go", "Go", 0, 49, 0, 0, 0, 0, 0, 14, 5, 68
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/balance_leader.go", "Go", 0, 136, 0, 0, 0, 0, 0, 26, 22, 184
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/balance_region.go", "Go", 0, 53, 0, 0, 0, 0, 0, 17, 13, 83
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/balance_test.go", "Go", 0, 296, 0, 0, 0, 0, 0, 113, 91, 500
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/base_scheduler.go", "Go", 0, 54, 0, 0, 0, 0, 0, 14, 17, 85
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/utils.go", "Go", 0, 31, 0, 0, 0, 0, 0, 13, 10, 54
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/utils_test.go", "Go", 0, 50, 0, 0, 0, 0, 0, 12, 14, 76
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/server.go", "Go", 0, 503, 0, 0, 0, 0, 0, 84, 97, 684
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/server_test.go", "Go", 0, 122, 0, 0, 0, 0, 0, 19, 26, 167
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/testutil.go", "Go", 0, 78, 0, 0, 0, 0, 0, 19, 21, 118
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/tso/tso.go", "Go", 0, 183, 0, 0, 0, 0, 0, 43, 39, 265
"/Users/<USER>/WorkSpace/tinykv/scheduler/server/util.go", "Go", 0, 111, 0, 0, 0, 0, 0, 25, 23, 159
"/Users/<USER>/WorkSpace/tinykv/scheduler/tests/client/client_test.go", "Go", 0, 140, 0, 0, 0, 0, 0, 17, 24, 181
"/Users/<USER>/WorkSpace/tinykv/scheduler/tests/cluster.go", "Go", 0, 346, 0, 0, 0, 0, 0, 61, 53, 460
"/Users/<USER>/WorkSpace/tinykv/scheduler/tests/config.go", "Go", 0, 71, 0, 0, 0, 0, 0, 12, 14, 97
"/Users/<USER>/WorkSpace/tinykv/scheduler/tests/server/id/id_test.go", "Go", 0, 77, 0, 0, 0, 0, 0, 12, 21, 110
"/Users/<USER>/WorkSpace/tinykv/scheduler/tests/server/server_test.go", "Go", 0, 81, 0, 0, 0, 0, 0, 18, 23, 122
"/Users/<USER>/WorkSpace/tinykv/scheduler/tests/server/tso/tso_test.go", "Go", 0, 89, 0, 0, 0, 0, 0, 12, 23, 124
"/Users/<USER>/WorkSpace/tinykv/scheduler/tools/Makefile", "Makefile", 0, 0, 0, 0, 0, 13, 0, 0, 7, 20
"/Users/<USER>/WorkSpace/tinykv/scheduler/tools/go.mod", "Go Module File", 0, 0, 0, 0, 0, 0, 13, 0, 3, 16
"/Users/<USER>/WorkSpace/tinykv/scheduler/tools/go.sum", "Go Checksum File", 0, 0, 0, 0, 243, 0, 0, 0, 1, 244
"/Users/<USER>/WorkSpace/tinykv/scheduler/tools/mod_guard.go", "Go", 0, 9, 0, 0, 0, 0, 0, 1, 3, 13
"/Users/<USER>/WorkSpace/tinykv/scripts/.get_perf.sh", "Shell Script", 0, 0, 0, 77, 0, 0, 0, 1, 11, 89
"/Users/<USER>/WorkSpace/tinykv/scripts/.make_target.sh", "Shell Script", 0, 0, 0, 9, 0, 0, 0, 1, 2, 12
"/Users/<USER>/WorkSpace/tinykv/scripts/Competition_Final_get_perf.sh", "Shell Script", 0, 0, 0, 87, 0, 0, 0, 1, 10, 98
"/Users/<USER>/WorkSpace/tinykv/scripts/classroom.yml", "YAML", 0, 0, 12, 0, 0, 0, 0, 0, 3, 15
"Total", "-", 544, 67793, 29, 231, 715, 121, 53, 5078, 7294, 81858