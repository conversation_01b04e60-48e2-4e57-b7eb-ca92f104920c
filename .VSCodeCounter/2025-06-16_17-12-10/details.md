# Details

Date : 2025-06-16 17:12:10

Directory /Users/<USER>/WorkSpace/tinykv

Total : 225 files,  69486 codes, 5078 comments, 7294 blanks, all 81858 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.github/workflows/go.yml](/.github/workflows/go.yml) | YAML | 16 | 0 | 5 | 21 |
| [Makefile](/Makefile) | Makefile | 103 | 2 | 29 | 134 |
| [README.md](/README.md) | Markdown | 74 | 0 | 36 | 110 |
| [doc/project1-StandaloneKV.md](/doc/project1-StandaloneKV.md) | Markdown | 30 | 0 | 18 | 48 |
| [doc/project2-RaftKV.md](/doc/project2-RaftKV.md) | Markdown | 152 | 0 | 81 | 233 |
| [doc/project3-MultiRaftKV.md](/doc/project3-MultiRaftKV.md) | Markdown | 111 | 0 | 77 | 188 |
| [doc/project4-Transaction.md](/doc/project4-Transaction.md) | Markdown | 50 | 0 | 39 | 89 |
| [doc/reading\_list.md](/doc/reading_list.md) | Markdown | 119 | 0 | 1 | 120 |
| [go.mod](/go.mod) | Go Module File | 34 | 0 | 4 | 38 |
| [go.sum](/go.sum) | Go Checksum File | 438 | 0 | 1 | 439 |
| [kv/config/config.go](/kv/config/config.go) | Go | 84 | 10 | 17 | 111 |
| [kv/coprocessor/analyze.go](/kv/coprocessor/analyze.go) | Go | 269 | 14 | 23 | 306 |
| [kv/coprocessor/closure\_exec.go](/kv/coprocessor/closure_exec.go) | Go | 658 | 15 | 67 | 740 |
| [kv/coprocessor/cop\_handler.go](/kv/coprocessor/cop_handler.go) | Go | 284 | 23 | 34 | 341 |
| [kv/coprocessor/rowcodec/common.go](/kv/coprocessor/rowcodec/common.go) | Go | 194 | 7 | 20 | 221 |
| [kv/coprocessor/rowcodec/decoder.go](/kv/coprocessor/rowcodec/decoder.go) | Go | 187 | 13 | 10 | 210 |
| [kv/coprocessor/rowcodec/encoder.go](/kv/coprocessor/rowcodec/encoder.go) | Go | 262 | 9 | 33 | 304 |
| [kv/coprocessor/topn.go](/kv/coprocessor/topn.go) | Go | 95 | 21 | 24 | 140 |
| [kv/main.go](/kv/main.go) | Go | 90 | 0 | 11 | 101 |
| [kv/raftstore/bootstrap.go](/kv/raftstore/bootstrap.go) | Go | 142 | 1 | 12 | 155 |
| [kv/raftstore/bootstrap\_test.go](/kv/raftstore/bootstrap_test.go) | Go | 33 | 0 | 6 | 39 |
| [kv/raftstore/cmd\_resp.go](/kv/raftstore/cmd_resp.go) | Go | 53 | 0 | 10 | 63 |
| [kv/raftstore/message/callback.go](/kv/raftstore/message/callback.go) | Go | 39 | 0 | 8 | 47 |
| [kv/raftstore/message/msg.go](/kv/raftstore/message/msg.go) | Go | 43 | 17 | 11 | 71 |
| [kv/raftstore/message/raft\_router.go](/kv/raftstore/message/raft_router.go) | Go | 10 | 0 | 3 | 13 |
| [kv/raftstore/meta/keys.go](/kv/raftstore/meta/keys.go) | Go | 94 | 10 | 20 | 124 |
| [kv/raftstore/meta/values.go](/kv/raftstore/meta/values.go) | Go | 87 | 3 | 10 | 100 |
| [kv/raftstore/node.go](/kv/raftstore/node.go) | Go | 187 | 0 | 26 | 213 |
| [kv/raftstore/peer.go](/kv/raftstore/peer.go) | Go | 279 | 64 | 50 | 393 |
| [kv/raftstore/peer\_msg\_handler.go](/kv/raftstore/peer_msg_handler.go) | Go | 474 | 54 | 46 | 574 |
| [kv/raftstore/peer\_storage.go](/kv/raftstore/peer_storage.go) | Go | 286 | 28 | 34 | 348 |
| [kv/raftstore/peer\_storage\_test.go](/kv/raftstore/peer_storage_test.go) | Go | 228 | 4 | 16 | 248 |
| [kv/raftstore/raft\_worker.go](/kv/raftstore/raft_worker.go) | Go | 57 | 7 | 9 | 73 |
| [kv/raftstore/raftstore.go](/kv/raftstore/raftstore.go) | Go | 273 | 15 | 27 | 315 |
| [kv/raftstore/router.go](/kv/raftstore/router.go) | Go | 84 | 2 | 19 | 105 |
| [kv/raftstore/runner/raftlog\_gc.go](/kv/raftstore/runner/raftlog_gc.go) | Go | 78 | 2 | 11 | 91 |
| [kv/raftstore/runner/region\_task.go](/kv/raftstore/runner/region_task.go) | Go | 170 | 12 | 28 | 210 |
| [kv/raftstore/runner/runner\_test.go](/kv/raftstore/runner/runner_test.go) | Go | 241 | 7 | 31 | 279 |
| [kv/raftstore/runner/scheduler\_task.go](/kv/raftstore/runner/scheduler_task.go) | Go | 133 | 0 | 20 | 153 |
| [kv/raftstore/runner/split\_checker.go](/kv/raftstore/runner/split_checker.go) | Go | 114 | 6 | 15 | 135 |
| [kv/raftstore/scheduler\_client/client.go](/kv/raftstore/scheduler_client/client.go) | Go | 478 | 16 | 62 | 556 |
| [kv/raftstore/snap/snap.go](/kv/raftstore/snap/snap.go) | Go | 696 | 31 | 60 | 787 |
| [kv/raftstore/snap/snap\_builder.go](/kv/raftstore/snap/snap_builder.go) | Go | 53 | 1 | 7 | 61 |
| [kv/raftstore/snap/snap\_manager.go](/kv/raftstore/snap/snap_manager.go) | Go | 320 | 1 | 25 | 346 |
| [kv/raftstore/snap/snap\_test.go](/kv/raftstore/snap/snap_test.go) | Go | 198 | 12 | 32 | 242 |
| [kv/raftstore/store\_worker.go](/kv/raftstore/store_worker.go) | Go | 271 | 21 | 25 | 317 |
| [kv/raftstore/ticker.go](/kv/raftstore/ticker.go) | Go | 110 | 3 | 18 | 131 |
| [kv/raftstore/util/error.go](/kv/raftstore/util/error.go) | Go | 66 | 0 | 16 | 82 |
| [kv/raftstore/util/error\_test.go](/kv/raftstore/util/error_test.go) | Go | 38 | 0 | 10 | 48 |
| [kv/raftstore/util/test\_util.go](/kv/raftstore/util/test_util.go) | Go | 35 | 0 | 4 | 39 |
| [kv/raftstore/util/util.go](/kv/raftstore/util/util.go) | Go | 150 | 29 | 29 | 208 |
| [kv/raftstore/util/util\_test.go](/kv/raftstore/util/util_test.go) | Go | 165 | 7 | 22 | 194 |
| [kv/server/raw\_api.go](/kv/server/raw_api.go) | Go | 17 | 13 | 7 | 37 |
| [kv/server/server.go](/kv/server/server.go) | Go | 69 | 17 | 19 | 105 |
| [kv/server/server\_test.go](/kv/server/server_test.go) | Go | 294 | 0 | 56 | 350 |
| [kv/storage/mem\_storage.go](/kv/storage/mem_storage.go) | Go | 215 | 4 | 33 | 252 |
| [kv/storage/modify.go](/kv/storage/modify.go) | Go | 37 | 1 | 8 | 46 |
| [kv/storage/raft\_storage/raft\_client.go](/kv/storage/raft_storage/raft_client.go) | Go | 115 | 1 | 15 | 131 |
| [kv/storage/raft\_storage/raft\_server.go](/kv/storage/raft_storage/raft_server.go) | Go | 190 | 3 | 27 | 220 |
| [kv/storage/raft\_storage/region\_reader.go](/kv/storage/raft_storage/region_reader.go) | Go | 73 | 2 | 16 | 91 |
| [kv/storage/raft\_storage/resolver.go](/kv/storage/raft_storage/resolver.go) | Go | 55 | 2 | 11 | 68 |
| [kv/storage/raft\_storage/snap\_runner.go](/kv/storage/raft_storage/snap_runner.go) | Go | 161 | 0 | 22 | 183 |
| [kv/storage/raft\_storage/transport.go](/kv/storage/raft_storage/transport.go) | Go | 83 | 1 | 12 | 96 |
| [kv/storage/standalone\_storage/standalone\_storage.go](/kv/storage/standalone_storage/standalone_storage.go) | Go | 23 | 8 | 8 | 39 |
| [kv/storage/storage.go](/kv/storage/storage.go) | Go | 16 | 3 | 4 | 23 |
| [kv/test\_raftstore/cluster.go](/kv/test_raftstore/cluster.go) | Go | 425 | 4 | 45 | 474 |
| [kv/test\_raftstore/cluster\_test.go](/kv/test_raftstore/cluster_test.go) | Go | 20 | 0 | 3 | 23 |
| [kv/test\_raftstore/filter.go](/kv/test_raftstore/filter.go) | Go | 36 | 0 | 10 | 46 |
| [kv/test\_raftstore/node.go](/kv/test_raftstore/node.go) | Go | 178 | 0 | 41 | 219 |
| [kv/test\_raftstore/scheduler.go](/kv/test_raftstore/scheduler.go) | Go | 475 | 16 | 78 | 569 |
| [kv/test\_raftstore/test\_test.go](/kv/test_raftstore/test_test.go) | Go | 550 | 95 | 97 | 742 |
| [kv/test\_raftstore/utils.go](/kv/test_raftstore/utils.go) | Go | 119 | 0 | 19 | 138 |
| [kv/transaction/commands4b\_test.go](/kv/transaction/commands4b_test.go) | Go | 488 | 37 | 72 | 597 |
| [kv/transaction/commands4c\_test.go](/kv/transaction/commands4c_test.go) | Go | 396 | 27 | 62 | 485 |
| [kv/transaction/commands\_test.go](/kv/transaction/commands_test.go) | Go | 172 | 16 | 24 | 212 |
| [kv/transaction/doc.go](/kv/transaction/doc.go) | Go | 1 | 38 | 2 | 41 |
| [kv/transaction/latches/latches.go](/kv/transaction/latches/latches.go) | Go | 57 | 29 | 14 | 100 |
| [kv/transaction/latches/latches\_test.go](/kv/transaction/latches/latches_test.go) | Go | 22 | 3 | 6 | 31 |
| [kv/transaction/mvcc/lock.go](/kv/transaction/mvcc/lock.go) | Go | 81 | 4 | 14 | 99 |
| [kv/transaction/mvcc/scanner.go](/kv/transaction/mvcc/scanner.go) | Go | 11 | 9 | 5 | 25 |
| [kv/transaction/mvcc/transaction.go](/kv/transaction/mvcc/transaction.go) | Go | 73 | 32 | 21 | 126 |
| [kv/transaction/mvcc/transaction\_test.go](/kv/transaction/mvcc/transaction_test.go) | Go | 246 | 7 | 49 | 302 |
| [kv/transaction/mvcc/write.go](/kv/transaction/mvcc/write.go) | Go | 55 | 3 | 12 | 70 |
| [kv/util/codec/codec.go](/kv/util/codec/codec.go) | Go | 56 | 17 | 12 | 85 |
| [kv/util/engine\_util/cf\_iterator.go](/kv/util/engine_util/cf_iterator.go) | Go | 87 | 17 | 26 | 130 |
| [kv/util/engine\_util/doc.go](/kv/util/engine_util/doc.go) | Go | 1 | 15 | 2 | 18 |
| [kv/util/engine\_util/engine\_util\_test.go](/kv/util/engine_util/engine_util_test.go) | Go | 84 | 0 | 10 | 94 |
| [kv/util/engine\_util/engines.go](/kv/util/engine_util/engines.go) | Go | 66 | 8 | 10 | 84 |
| [kv/util/engine\_util/util.go](/kv/util/engine_util/util.go) | Go | 96 | 0 | 15 | 111 |
| [kv/util/engine\_util/write\_batch.go](/kv/util/engine_util/write_batch.go) | Go | 96 | 0 | 15 | 111 |
| [kv/util/file.go](/kv/util/file.go) | Go | 49 | 1 | 8 | 58 |
| [kv/util/worker/worker.go](/kv/util/worker/worker.go) | Go | 49 | 0 | 12 | 61 |
| [log/log.go](/log/log.go) | Go | 223 | 6 | 54 | 283 |
| [proto/generate\_go.sh](/proto/generate_go.sh) | Shell Script | 55 | 3 | 14 | 72 |
| [proto/pkg/coprocessor/coprocessor.pb.go](/proto/pkg/coprocessor/coprocessor.pb.go) | Go | 1,086 | 9 | 46 | 1,141 |
| [proto/pkg/eraftpb/eraftpb.pb.go](/proto/pkg/eraftpb/eraftpb.pb.go) | Go | 2,144 | 43 | 97 | 2,284 |
| [proto/pkg/errorpb/errorpb.pb.go](/proto/pkg/errorpb/errorpb.pb.go) | Go | 1,767 | 8 | 78 | 1,853 |
| [proto/pkg/kvrpcpb/kvrpcpb.pb.go](/proto/pkg/kvrpcpb/kvrpcpb.pb.go) | Go | 7,685 | 62 | 308 | 8,055 |
| [proto/pkg/metapb/metapb.pb.go](/proto/pkg/metapb/metapb.pb.go) | Go | 1,318 | 14 | 64 | 1,396 |
| [proto/pkg/raft\_cmdpb/raft\_cmdpb.pb.go](/proto/pkg/raft_cmdpb/raft_cmdpb.pb.go) | Go | 5,572 | 18 | 240 | 5,830 |
| [proto/pkg/raft\_serverpb/raft\_serverpb.pb.go](/proto/pkg/raft_serverpb/raft_serverpb.pb.go) | Go | 2,964 | 24 | 130 | 3,118 |
| [proto/pkg/schedulerpb/schedulerpb.pb.go](/proto/pkg/schedulerpb/schedulerpb.pb.go) | Go | 13,305 | 65 | 586 | 13,956 |
| [proto/pkg/tinykvpb/tinykvpb.pb.go](/proto/pkg/tinykvpb/tinykvpb.pb.go) | Go | 559 | 21 | 68 | 648 |
| [proto/tools/Makefile](/proto/tools/Makefile) | Makefile | 5 | 0 | 2 | 7 |
| [proto/tools/go.mod](/proto/tools/go.mod) | Go Module File | 6 | 0 | 3 | 9 |
| [proto/tools/go.sum](/proto/tools/go.sum) | Go Checksum File | 34 | 0 | 1 | 35 |
| [proto/tools/mod\_guard.go](/proto/tools/mod_guard.go) | Go | 5 | 1 | 3 | 9 |
| [raft/doc.go](/raft/doc.go) | Go | 1 | 266 | 2 | 269 |
| [raft/log.go](/raft/log.go) | Go | 30 | 53 | 17 | 100 |
| [raft/raft.go](/raft/raft.go) | Go | 101 | 95 | 44 | 240 |
| [raft/raft\_paper\_test.go](/raft/raft_paper_test.go) | Go | 717 | 138 | 74 | 929 |
| [raft/raft\_test.go](/raft/raft_test.go) | Go | 1,274 | 210 | 243 | 1,727 |
| [raft/rawnode.go](/raft/rawnode.go) | Go | 94 | 57 | 26 | 177 |
| [raft/rawnode\_test.go](/raft/rawnode_test.go) | Go | 200 | 25 | 28 | 253 |
| [raft/storage.go](/raft/storage.go) | Go | 164 | 75 | 35 | 274 |
| [raft/util.go](/raft/util.go) | Go | 96 | 16 | 18 | 130 |
| [scheduler/client/client.go](/scheduler/client/client.go) | Go | 626 | 60 | 97 | 783 |
| [scheduler/client/client\_test.go](/scheduler/client/client_test.go) | Go | 352 | 26 | 43 | 421 |
| [scheduler/main.go](/scheduler/main.go) | Go | 85 | 16 | 22 | 123 |
| [scheduler/pkg/apiutil/apiutil.go](/scheduler/pkg/apiutil/apiutil.go) | Go | 11 | 14 | 5 | 30 |
| [scheduler/pkg/btree/.travis.yml](/scheduler/pkg/btree/.travis.yml) | YAML | 1 | 0 | 1 | 2 |
| [scheduler/pkg/btree/README.md](/scheduler/pkg/btree/README.md) | Markdown | 8 | 0 | 5 | 13 |
| [scheduler/pkg/btree/btree.go](/scheduler/pkg/btree/btree.go) | Go | 729 | 270 | 90 | 1,089 |
| [scheduler/pkg/btree/btree\_mem.go](/scheduler/pkg/btree/btree_mem.go) | Go | 55 | 15 | 7 | 77 |
| [scheduler/pkg/btree/btree\_test.go](/scheduler/pkg/btree/btree_test.go) | Go | 782 | 38 | 47 | 867 |
| [scheduler/pkg/cache/cache.go](/scheduler/pkg/cache/cache.go) | Go | 13 | 20 | 4 | 37 |
| [scheduler/pkg/cache/cache\_test.go](/scheduler/pkg/cache/cache_test.go) | Go | 50 | 12 | 21 | 83 |
| [scheduler/pkg/cache/ttl.go](/scheduler/pkg/cache/ttl.go) | Go | 103 | 23 | 27 | 153 |
| [scheduler/pkg/codec/codec.go](/scheduler/pkg/codec/codec.go) | Go | 103 | 38 | 23 | 164 |
| [scheduler/pkg/codec/codec\_test.go](/scheduler/pkg/codec/codec_test.go) | Go | 30 | 12 | 13 | 55 |
| [scheduler/pkg/etcdutil/etcdutil.go](/scheduler/pkg/etcdutil/etcdutil.go) | Go | 99 | 24 | 19 | 142 |
| [scheduler/pkg/etcdutil/etcdutil\_test.go](/scheduler/pkg/etcdutil/etcdutil_test.go) | Go | 76 | 15 | 20 | 111 |
| [scheduler/pkg/grpcutil/grpcutil.go](/scheduler/pkg/grpcutil/grpcutil.go) | Go | 45 | 16 | 9 | 70 |
| [scheduler/pkg/logutil/log.go](/scheduler/pkg/logutil/log.go) | Go | 211 | 51 | 35 | 297 |
| [scheduler/pkg/logutil/log\_test.go](/scheduler/pkg/logutil/log_test.go) | Go | 79 | 14 | 19 | 112 |
| [scheduler/pkg/mock/mockcluster/mockcluster.go](/scheduler/pkg/mock/mockcluster/mockcluster.go) | Go | 267 | 46 | 41 | 354 |
| [scheduler/pkg/mock/mockhbstream/mockhbstream.go](/scheduler/pkg/mock/mockhbstream/mockhbstream.go) | Go | 67 | 22 | 15 | 104 |
| [scheduler/pkg/mock/mockid/mockid.go](/scheduler/pkg/mock/mockid/mockid.go) | Go | 11 | 15 | 6 | 32 |
| [scheduler/pkg/mock/mockoption/mockoption.go](/scheduler/pkg/mock/mockoption/mockoption.go) | Go | 63 | 23 | 14 | 100 |
| [scheduler/pkg/slice/slice.go](/scheduler/pkg/slice/slice.go) | Go | 20 | 15 | 6 | 41 |
| [scheduler/pkg/slice/slice\_test.go](/scheduler/pkg/slice/slice_test.go) | Go | 31 | 12 | 9 | 52 |
| [scheduler/pkg/tempurl/tempurl.go](/scheduler/pkg/tempurl/tempurl.go) | Go | 41 | 13 | 8 | 62 |
| [scheduler/pkg/testutil/operator\_check.go](/scheduler/pkg/testutil/operator_check.go) | Go | 48 | 19 | 8 | 75 |
| [scheduler/pkg/testutil/testutil.go](/scheduler/pkg/testutil/testutil.go) | Go | 38 | 19 | 11 | 68 |
| [scheduler/pkg/tsoutil/tso.go](/scheduler/pkg/tsoutil/tso.go) | Go | 12 | 13 | 5 | 30 |
| [scheduler/pkg/typeutil/convension.go](/scheduler/pkg/typeutil/convension.go) | Go | 16 | 14 | 7 | 37 |
| [scheduler/pkg/typeutil/duration.go](/scheduler/pkg/typeutil/duration.go) | Go | 33 | 17 | 9 | 59 |
| [scheduler/pkg/typeutil/duration\_test.go](/scheduler/pkg/typeutil/duration_test.go) | Go | 26 | 12 | 12 | 50 |
| [scheduler/pkg/typeutil/size.go](/scheduler/pkg/typeutil/size.go) | Go | 30 | 16 | 8 | 54 |
| [scheduler/pkg/typeutil/size\_test.go](/scheduler/pkg/typeutil/size_test.go) | Go | 24 | 12 | 10 | 46 |
| [scheduler/pkg/typeutil/string\_slice.go](/scheduler/pkg/typeutil/string_slice.go) | Go | 22 | 15 | 7 | 44 |
| [scheduler/pkg/typeutil/string\_slice\_test.go](/scheduler/pkg/typeutil/string_slice_test.go) | Go | 27 | 12 | 10 | 49 |
| [scheduler/pkg/typeutil/time.go](/scheduler/pkg/typeutil/time.go) | Go | 13 | 15 | 7 | 35 |
| [scheduler/pkg/typeutil/time\_test.go](/scheduler/pkg/typeutil/time_test.go) | Go | 30 | 12 | 8 | 50 |
| [scheduler/scripts/build-api.sh](/scheduler/scripts/build-api.sh) | Shell Script | 3 | 2 | 4 | 9 |
| [scheduler/server/cluster.go](/scheduler/server/cluster.go) | Go | 637 | 92 | 131 | 860 |
| [scheduler/server/cluster\_test.go](/scheduler/server/cluster_test.go) | Go | 905 | 68 | 182 | 1,155 |
| [scheduler/server/cluster\_worker.go](/scheduler/server/cluster_worker.go) | Go | 109 | 16 | 22 | 147 |
| [scheduler/server/cluster\_worker\_test.go](/scheduler/server/cluster_worker_test.go) | Go | 94 | 12 | 16 | 122 |
| [scheduler/server/config/config.go](/scheduler/server/config/config.go) | Go | 466 | 80 | 110 | 656 |
| [scheduler/server/config/option.go](/scheduler/server/config/option.go) | Go | 120 | 37 | 27 | 184 |
| [scheduler/server/coordinator.go](/scheduler/server/coordinator.go) | Go | 292 | 28 | 48 | 368 |
| [scheduler/server/coordinator\_test.go](/scheduler/server/coordinator_test.go) | Go | 643 | 52 | 109 | 804 |
| [scheduler/server/core/basic\_cluster.go](/scheduler/server/core/basic_cluster.go) | Go | 247 | 57 | 50 | 354 |
| [scheduler/server/core/errors.go](/scheduler/server/core/errors.go) | Go | 26 | 23 | 15 | 64 |
| [scheduler/server/core/kind.go](/scheduler/server/core/kind.go) | Go | 30 | 19 | 9 | 58 |
| [scheduler/server/core/region.go](/scheduler/server/core/region.go) | Go | 614 | 103 | 107 | 824 |
| [scheduler/server/core/region\_option.go](/scheduler/server/core/region_option.go) | Go | 84 | 27 | 18 | 129 |
| [scheduler/server/core/region\_test.go](/scheduler/server/core/region_test.go) | Go | 150 | 18 | 25 | 193 |
| [scheduler/server/core/region\_tree.go](/scheduler/server/core/region_tree.go) | Go | 163 | 35 | 34 | 232 |
| [scheduler/server/core/region\_tree\_test.go](/scheduler/server/core/region_tree_test.go) | Go | 295 | 23 | 53 | 371 |
| [scheduler/server/core/storage.go](/scheduler/server/core/storage.go) | Go | 177 | 30 | 33 | 240 |
| [scheduler/server/core/storage\_test.go](/scheduler/server/core/storage_test.go) | Go | 86 | 12 | 20 | 118 |
| [scheduler/server/core/store.go](/scheduler/server/core/store.go) | Go | 327 | 77 | 68 | 472 |
| [scheduler/server/core/store\_option.go](/scheduler/server/core/store_option.go) | Go | 82 | 27 | 19 | 128 |
| [scheduler/server/core/store\_test.go](/scheduler/server/core/store_test.go) | Go | 38 | 12 | 7 | 57 |
| [scheduler/server/core/test\_util.go](/scheduler/server/core/test_util.go) | Go | 89 | 17 | 9 | 115 |
| [scheduler/server/grpc\_service.go](/scheduler/server/grpc_service.go) | Go | 541 | 72 | 96 | 709 |
| [scheduler/server/heartbeat\_streams.go](/scheduler/server/heartbeat_streams.go) | Go | 139 | 12 | 21 | 172 |
| [scheduler/server/id/id.go](/scheduler/server/id/id.go) | Go | 78 | 18 | 21 | 117 |
| [scheduler/server/kv/etcd\_kv.go](/scheduler/server/kv/etcd_kv.go) | Go | 123 | 21 | 23 | 167 |
| [scheduler/server/kv/etcd\_kv\_test.go](/scheduler/server/kv/etcd_kv_test.go) | Go | 87 | 13 | 19 | 119 |
| [scheduler/server/kv/kv.go](/scheduler/server/kv/kv.go) | Go | 7 | 13 | 3 | 23 |
| [scheduler/server/kv/mem\_kv.go](/scheduler/server/kv/mem_kv.go) | Go | 53 | 13 | 13 | 79 |
| [scheduler/server/member/leader.go](/scheduler/server/member/leader.go) | Go | 299 | 57 | 45 | 401 |
| [scheduler/server/member/lease.go](/scheduler/server/member/lease.go) | Go | 101 | 22 | 18 | 141 |
| [scheduler/server/schedule/checker/replica\_checker.go](/scheduler/server/schedule/checker/replica_checker.go) | Go | 153 | 28 | 24 | 205 |
| [scheduler/server/schedule/checker\_controller.go](/scheduler/server/schedule/checker_controller.go) | Go | 31 | 18 | 7 | 56 |
| [scheduler/server/schedule/filter/filters.go](/scheduler/server/schedule/filter/filters.go) | Go | 162 | 34 | 39 | 235 |
| [scheduler/server/schedule/operator/operator.go](/scheduler/server/schedule/operator/operator.go) | Go | 313 | 69 | 53 | 435 |
| [scheduler/server/schedule/operator/operator\_kind.go](/scheduler/server/schedule/operator/operator_kind.go) | Go | 60 | 15 | 11 | 86 |
| [scheduler/server/schedule/operator/operator\_test.go](/scheduler/server/schedule/operator/operator_test.go) | Go | 144 | 15 | 19 | 178 |
| [scheduler/server/schedule/operator\_controller.go](/scheduler/server/schedule/operator_controller.go) | Go | 274 | 47 | 43 | 364 |
| [scheduler/server/schedule/operator\_controller\_test.go](/scheduler/server/schedule/operator_controller_test.go) | Go | 98 | 18 | 19 | 135 |
| [scheduler/server/schedule/operator\_queue.go](/scheduler/server/schedule/operator_queue.go) | Go | 31 | 12 | 11 | 54 |
| [scheduler/server/schedule/opt/opts.go](/scheduler/server/schedule/opt/opts.go) | Go | 20 | 17 | 10 | 47 |
| [scheduler/server/schedule/scheduler.go](/scheduler/server/schedule/scheduler.go) | Go | 93 | 28 | 19 | 140 |
| [scheduler/server/schedule/selector/selector.go](/scheduler/server/schedule/selector/selector.go) | Go | 89 | 27 | 14 | 130 |
| [scheduler/server/schedule/selector/selector\_test.go](/scheduler/server/schedule/selector/selector_test.go) | Go | 26 | 12 | 11 | 49 |
| [scheduler/server/schedule/test\_util.go](/scheduler/server/schedule/test_util.go) | Go | 49 | 14 | 5 | 68 |
| [scheduler/server/schedulers/balance\_leader.go](/scheduler/server/schedulers/balance_leader.go) | Go | 136 | 26 | 22 | 184 |
| [scheduler/server/schedulers/balance\_region.go](/scheduler/server/schedulers/balance_region.go) | Go | 53 | 17 | 13 | 83 |
| [scheduler/server/schedulers/balance\_test.go](/scheduler/server/schedulers/balance_test.go) | Go | 296 | 113 | 91 | 500 |
| [scheduler/server/schedulers/base\_scheduler.go](/scheduler/server/schedulers/base_scheduler.go) | Go | 54 | 14 | 17 | 85 |
| [scheduler/server/schedulers/utils.go](/scheduler/server/schedulers/utils.go) | Go | 31 | 13 | 10 | 54 |
| [scheduler/server/schedulers/utils\_test.go](/scheduler/server/schedulers/utils_test.go) | Go | 50 | 12 | 14 | 76 |
| [scheduler/server/server.go](/scheduler/server/server.go) | Go | 503 | 84 | 97 | 684 |
| [scheduler/server/server\_test.go](/scheduler/server/server_test.go) | Go | 122 | 19 | 26 | 167 |
| [scheduler/server/testutil.go](/scheduler/server/testutil.go) | Go | 78 | 19 | 21 | 118 |
| [scheduler/server/tso/tso.go](/scheduler/server/tso/tso.go) | Go | 183 | 43 | 39 | 265 |
| [scheduler/server/util.go](/scheduler/server/util.go) | Go | 111 | 25 | 23 | 159 |
| [scheduler/tests/client/client\_test.go](/scheduler/tests/client/client_test.go) | Go | 140 | 17 | 24 | 181 |
| [scheduler/tests/cluster.go](/scheduler/tests/cluster.go) | Go | 346 | 61 | 53 | 460 |
| [scheduler/tests/config.go](/scheduler/tests/config.go) | Go | 71 | 12 | 14 | 97 |
| [scheduler/tests/server/id/id\_test.go](/scheduler/tests/server/id/id_test.go) | Go | 77 | 12 | 21 | 110 |
| [scheduler/tests/server/server\_test.go](/scheduler/tests/server/server_test.go) | Go | 81 | 18 | 23 | 122 |
| [scheduler/tests/server/tso/tso\_test.go](/scheduler/tests/server/tso/tso_test.go) | Go | 89 | 12 | 23 | 124 |
| [scheduler/tools/Makefile](/scheduler/tools/Makefile) | Makefile | 13 | 0 | 7 | 20 |
| [scheduler/tools/go.mod](/scheduler/tools/go.mod) | Go Module File | 13 | 0 | 3 | 16 |
| [scheduler/tools/go.sum](/scheduler/tools/go.sum) | Go Checksum File | 243 | 0 | 1 | 244 |
| [scheduler/tools/mod\_guard.go](/scheduler/tools/mod_guard.go) | Go | 9 | 1 | 3 | 13 |
| [scripts/.get\_perf.sh](/scripts/.get_perf.sh) | Shell Script | 77 | 1 | 11 | 89 |
| [scripts/.make\_target.sh](/scripts/.make_target.sh) | Shell Script | 9 | 1 | 2 | 12 |
| [scripts/Competition\_Final\_get\_perf.sh](/scripts/Competition_Final_get_perf.sh) | Shell Script | 87 | 1 | 10 | 98 |
| [scripts/classroom.yml](/scripts/classroom.yml) | YAML | 12 | 0 | 3 | 15 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)