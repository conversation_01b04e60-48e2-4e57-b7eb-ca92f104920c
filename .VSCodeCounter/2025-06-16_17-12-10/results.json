{"file:///Users/<USER>/WorkSpace/tinykv/doc/project2-RaftKV.md": {"language": "<PERSON><PERSON>", "code": 152, "comment": 0, "blank": 81}, "file:///Users/<USER>/WorkSpace/tinykv/doc/project1-StandaloneKV.md": {"language": "<PERSON><PERSON>", "code": 30, "comment": 0, "blank": 18}, "file:///Users/<USER>/WorkSpace/tinykv/doc/project4-Transaction.md": {"language": "<PERSON><PERSON>", "code": 50, "comment": 0, "blank": 39}, "file:///Users/<USER>/WorkSpace/tinykv/doc/project3-MultiRaftKV.md": {"language": "<PERSON><PERSON>", "code": 111, "comment": 0, "blank": 77}, "file:///Users/<USER>/WorkSpace/tinykv/doc/reading_list.md": {"language": "<PERSON><PERSON>", "code": 119, "comment": 0, "blank": 1}, "file:///Users/<USER>/WorkSpace/tinykv/raft/rawnode_test.go": {"language": "Go", "code": 200, "comment": 25, "blank": 28}, "file:///Users/<USER>/WorkSpace/tinykv/log/log.go": {"language": "Go", "code": 223, "comment": 6, "blank": 54}, "file:///Users/<USER>/WorkSpace/tinykv/raft/storage.go": {"language": "Go", "code": 164, "comment": 75, "blank": 35}, "file:///Users/<USER>/WorkSpace/tinykv/raft/doc.go": {"language": "Go", "code": 1, "comment": 266, "blank": 2}, "file:///Users/<USER>/WorkSpace/tinykv/raft/raft.go": {"language": "Go", "code": 101, "comment": 95, "blank": 44}, "file:///Users/<USER>/WorkSpace/tinykv/raft/util.go": {"language": "Go", "code": 96, "comment": 16, "blank": 18}, "file:///Users/<USER>/WorkSpace/tinykv/raft/raft_test.go": {"language": "Go", "code": 1274, "comment": 210, "blank": 243}, "file:///Users/<USER>/WorkSpace/tinykv/raft/rawnode.go": {"language": "Go", "code": 94, "comment": 57, "blank": 26}, "file:///Users/<USER>/WorkSpace/tinykv/raft/log.go": {"language": "Go", "code": 30, "comment": 53, "blank": 17}, "file:///Users/<USER>/WorkSpace/tinykv/raft/raft_paper_test.go": {"language": "Go", "code": 717, "comment": 138, "blank": 74}, "file:///Users/<USER>/WorkSpace/tinykv/.github/workflows/go.yml": {"language": "YAML", "code": 16, "comment": 0, "blank": 5}, "file:///Users/<USER>/WorkSpace/tinykv/scripts/.make_target.sh": {"language": "<PERSON> Script", "code": 9, "comment": 1, "blank": 2}, "file:///Users/<USER>/WorkSpace/tinykv/scripts/.get_perf.sh": {"language": "<PERSON> Script", "code": 77, "comment": 1, "blank": 11}, "file:///Users/<USER>/WorkSpace/tinykv/scripts/Competition_Final_get_perf.sh": {"language": "<PERSON> Script", "code": 87, "comment": 1, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/scripts/classroom.yml": {"language": "YAML", "code": 12, "comment": 0, "blank": 3}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/testutil/testutil.go": {"language": "Go", "code": 38, "comment": 19, "blank": 11}, "file:///Users/<USER>/WorkSpace/tinykv/README.md": {"language": "<PERSON><PERSON>", "code": 74, "comment": 0, "blank": 36}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/testutil/operator_check.go": {"language": "Go", "code": 48, "comment": 19, "blank": 8}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/logutil/log_test.go": {"language": "Go", "code": 79, "comment": 14, "blank": 19}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/logutil/log.go": {"language": "Go", "code": 211, "comment": 51, "blank": 35}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/tempurl/tempurl.go": {"language": "Go", "code": 41, "comment": 13, "blank": 8}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/apiutil/apiutil.go": {"language": "Go", "code": 11, "comment": 14, "blank": 5}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/slice/slice_test.go": {"language": "Go", "code": 31, "comment": 12, "blank": 9}, "file:///Users/<USER>/WorkSpace/tinykv/go.sum": {"language": "Go Checksum File", "code": 438, "comment": 0, "blank": 1}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/slice/slice.go": {"language": "Go", "code": 20, "comment": 15, "blank": 6}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/etcdutil/etcdutil.go": {"language": "Go", "code": 99, "comment": 24, "blank": 19}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/etcdutil/etcdutil_test.go": {"language": "Go", "code": 76, "comment": 15, "blank": 20}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/btree/btree.go": {"language": "Go", "code": 729, "comment": 270, "blank": 90}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/size_test.go": {"language": "Go", "code": 24, "comment": 12, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/size.go": {"language": "Go", "code": 30, "comment": 16, "blank": 8}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/time.go": {"language": "Go", "code": 13, "comment": 15, "blank": 7}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/codec/codec.go": {"language": "Go", "code": 103, "comment": 38, "blank": 23}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/duration.go": {"language": "Go", "code": 33, "comment": 17, "blank": 9}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/mock/mockhbstream/mockhbstream.go": {"language": "Go", "code": 67, "comment": 22, "blank": 15}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/btree/btree_mem.go": {"language": "Go", "code": 55, "comment": 15, "blank": 7}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/tsoutil/tso.go": {"language": "Go", "code": 12, "comment": 13, "blank": 5}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/string_slice.go": {"language": "Go", "code": 22, "comment": 15, "blank": 7}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/convension.go": {"language": "Go", "code": 16, "comment": 14, "blank": 7}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/btree/.travis.yml": {"language": "YAML", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/duration_test.go": {"language": "Go", "code": 26, "comment": 12, "blank": 12}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/btree/README.md": {"language": "<PERSON><PERSON>", "code": 8, "comment": 0, "blank": 5}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/codec/codec_test.go": {"language": "Go", "code": 30, "comment": 12, "blank": 13}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/btree/btree_test.go": {"language": "Go", "code": 782, "comment": 38, "blank": 47}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/mock/mockid/mockid.go": {"language": "Go", "code": 11, "comment": 15, "blank": 6}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/string_slice_test.go": {"language": "Go", "code": 27, "comment": 12, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/main.go": {"language": "Go", "code": 85, "comment": 16, "blank": 22}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/mock/mockcluster/mockcluster.go": {"language": "Go", "code": 267, "comment": 46, "blank": 41}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/grpcutil/grpcutil.go": {"language": "Go", "code": 45, "comment": 16, "blank": 9}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/cache/ttl.go": {"language": "Go", "code": 103, "comment": 23, "blank": 27}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/client/client_test.go": {"language": "Go", "code": 352, "comment": 26, "blank": 43}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/typeutil/time_test.go": {"language": "Go", "code": 30, "comment": 12, "blank": 8}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/cache/cache.go": {"language": "Go", "code": 13, "comment": 20, "blank": 4}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/cache/cache_test.go": {"language": "Go", "code": 50, "comment": 12, "blank": 21}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/client/client.go": {"language": "Go", "code": 626, "comment": 60, "blank": 97}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/scripts/build-api.sh": {"language": "<PERSON> Script", "code": 3, "comment": 2, "blank": 4}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/tools/go.sum": {"language": "Go Checksum File", "code": 243, "comment": 0, "blank": 1}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/tools/Makefile": {"language": "<PERSON><PERSON><PERSON>", "code": 13, "comment": 0, "blank": 7}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/tools/mod_guard.go": {"language": "Go", "code": 9, "comment": 1, "blank": 3}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/config/option.go": {"language": "Go", "code": 120, "comment": 37, "blank": 27}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/tools/go.mod": {"language": "Go Module File", "code": 13, "comment": 0, "blank": 3}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/balance_region.go": {"language": "Go", "code": 53, "comment": 17, "blank": 13}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/cluster_test.go": {"language": "Go", "code": 905, "comment": 68, "blank": 182}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/tests/config.go": {"language": "Go", "code": 71, "comment": 12, "blank": 14}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/tests/cluster.go": {"language": "Go", "code": 346, "comment": 61, "blank": 53}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/utils.go": {"language": "Go", "code": 31, "comment": 13, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/balance_test.go": {"language": "Go", "code": 296, "comment": 113, "blank": 91}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/balance_leader.go": {"language": "Go", "code": 136, "comment": 26, "blank": 22}, "file:///Users/<USER>/WorkSpace/tinykv/Makefile": {"language": "<PERSON><PERSON><PERSON>", "code": 103, "comment": 2, "blank": 29}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/utils_test.go": {"language": "Go", "code": 50, "comment": 12, "blank": 14}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedulers/base_scheduler.go": {"language": "Go", "code": 54, "comment": 14, "blank": 17}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/testutil.go": {"language": "Go", "code": 78, "comment": 19, "blank": 21}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/config/config.go": {"language": "Go", "code": 466, "comment": 80, "blank": 110}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/pkg/mock/mockoption/mockoption.go": {"language": "Go", "code": 63, "comment": 23, "blank": 14}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/server_test.go": {"language": "Go", "code": 122, "comment": 19, "blank": 26}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/heartbeat_streams.go": {"language": "Go", "code": 139, "comment": 12, "blank": 21}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/tests/server/server_test.go": {"language": "Go", "code": 81, "comment": 18, "blank": 23}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/util.go": {"language": "Go", "code": 111, "comment": 25, "blank": 23}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/grpc_service.go": {"language": "Go", "code": 541, "comment": 72, "blank": 96}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/tests/server/tso/tso_test.go": {"language": "Go", "code": 89, "comment": 12, "blank": 23}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/cluster.go": {"language": "Go", "code": 637, "comment": 92, "blank": 131}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/coordinator_test.go": {"language": "Go", "code": 643, "comment": 52, "blank": 109}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/id/id.go": {"language": "Go", "code": 78, "comment": 18, "blank": 21}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/tso/tso.go": {"language": "Go", "code": 183, "comment": 43, "blank": 39}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/member/lease.go": {"language": "Go", "code": 101, "comment": 22, "blank": 18}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/tests/server/id/id_test.go": {"language": "Go", "code": 77, "comment": 12, "blank": 21}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/coordinator.go": {"language": "Go", "code": 292, "comment": 28, "blank": 48}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/member/leader.go": {"language": "Go", "code": 299, "comment": 57, "blank": 45}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/kv/etcd_kv.go": {"language": "Go", "code": 123, "comment": 21, "blank": 23}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/kv/kv.go": {"language": "Go", "code": 7, "comment": 13, "blank": 3}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/kv/etcd_kv_test.go": {"language": "Go", "code": 87, "comment": 13, "blank": 19}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/cluster_worker.go": {"language": "Go", "code": 109, "comment": 16, "blank": 22}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/cluster_worker_test.go": {"language": "Go", "code": 94, "comment": 12, "blank": 16}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/kind.go": {"language": "Go", "code": 30, "comment": 19, "blank": 9}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/server.go": {"language": "Go", "code": 503, "comment": 84, "blank": 97}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/region_tree.go": {"language": "Go", "code": 163, "comment": 35, "blank": 34}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/store_test.go": {"language": "Go", "code": 38, "comment": 12, "blank": 7}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/kv/mem_kv.go": {"language": "Go", "code": 53, "comment": 13, "blank": 13}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/store.go": {"language": "Go", "code": 327, "comment": 77, "blank": 68}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/test_util.go": {"language": "Go", "code": 89, "comment": 17, "blank": 9}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/region.go": {"language": "Go", "code": 614, "comment": 103, "blank": 107}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/region_tree_test.go": {"language": "Go", "code": 295, "comment": 23, "blank": 53}, "file:///Users/<USER>/WorkSpace/tinykv/go.mod": {"language": "Go Module File", "code": 34, "comment": 0, "blank": 4}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/region_option.go": {"language": "Go", "code": 84, "comment": 27, "blank": 18}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/region_test.go": {"language": "Go", "code": 150, "comment": 18, "blank": 25}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/errors.go": {"language": "Go", "code": 26, "comment": 23, "blank": 15}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/tests/client/client_test.go": {"language": "Go", "code": 140, "comment": 17, "blank": 24}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/storage_test.go": {"language": "Go", "code": 86, "comment": 12, "blank": 20}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/storage.go": {"language": "Go", "code": 177, "comment": 30, "blank": 33}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/store_option.go": {"language": "Go", "code": 82, "comment": 27, "blank": 19}, "file:///Users/<USER>/WorkSpace/tinykv/proto/tools/mod_guard.go": {"language": "Go", "code": 5, "comment": 1, "blank": 3}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/commands_test.go": {"language": "Go", "code": 172, "comment": 16, "blank": 24}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/core/basic_cluster.go": {"language": "Go", "code": 247, "comment": 57, "blank": 50}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/doc.go": {"language": "Go", "code": 1, "comment": 38, "blank": 2}, "file:///Users/<USER>/WorkSpace/tinykv/proto/tools/go.sum": {"language": "Go Checksum File", "code": 34, "comment": 0, "blank": 1}, "file:///Users/<USER>/WorkSpace/tinykv/proto/tools/Makefile": {"language": "<PERSON><PERSON><PERSON>", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/WorkSpace/tinykv/proto/tools/go.mod": {"language": "Go Module File", "code": 6, "comment": 0, "blank": 3}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/commands4b_test.go": {"language": "Go", "code": 488, "comment": 37, "blank": 72}, "file:///Users/<USER>/WorkSpace/tinykv/kv/storage/storage.go": {"language": "Go", "code": 16, "comment": 3, "blank": 4}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/latches/latches.go": {"language": "Go", "code": 57, "comment": 29, "blank": 14}, "file:///Users/<USER>/WorkSpace/tinykv/kv/main.go": {"language": "Go", "code": 90, "comment": 0, "blank": 11}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/scheduler.go": {"language": "Go", "code": 93, "comment": 28, "blank": 19}, "file:///Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/cluster_test.go": {"language": "Go", "code": 20, "comment": 0, "blank": 3}, "file:///Users/<USER>/WorkSpace/tinykv/kv/storage/modify.go": {"language": "Go", "code": 37, "comment": 1, "blank": 8}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/latches/latches_test.go": {"language": "Go", "code": 22, "comment": 3, "blank": 6}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/mvcc/transaction.go": {"language": "Go", "code": 73, "comment": 32, "blank": 21}, "file:///Users/<USER>/WorkSpace/tinykv/kv/storage/mem_storage.go": {"language": "Go", "code": 215, "comment": 4, "blank": 33}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/mvcc/transaction_test.go": {"language": "Go", "code": 246, "comment": 7, "blank": 49}, "file:///Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/test_test.go": {"language": "Go", "code": 550, "comment": 95, "blank": 97}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/mvcc/write.go": {"language": "Go", "code": 55, "comment": 3, "blank": 12}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/mvcc/lock.go": {"language": "Go", "code": 81, "comment": 4, "blank": 14}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/mvcc/scanner.go": {"language": "Go", "code": 11, "comment": 9, "blank": 5}, "file:///Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/utils.go": {"language": "Go", "code": 119, "comment": 0, "blank": 19}, "file:///Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/filter.go": {"language": "Go", "code": 36, "comment": 0, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/cluster.go": {"language": "Go", "code": 425, "comment": 4, "blank": 45}, "file:///Users/<USER>/WorkSpace/tinykv/kv/storage/standalone_storage/standalone_storage.go": {"language": "Go", "code": 23, "comment": 8, "blank": 8}, "file:///Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/transport.go": {"language": "Go", "code": 83, "comment": 1, "blank": 12}, "file:///Users/<USER>/WorkSpace/tinykv/kv/transaction/commands4c_test.go": {"language": "Go", "code": 396, "comment": 27, "blank": 62}, "file:///Users/<USER>/WorkSpace/tinykv/kv/server/raw_api.go": {"language": "Go", "code": 17, "comment": 13, "blank": 7}, "file:///Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/scheduler.go": {"language": "Go", "code": 475, "comment": 16, "blank": 78}, "file:///Users/<USER>/WorkSpace/tinykv/kv/test_raftstore/node.go": {"language": "Go", "code": 178, "comment": 0, "blank": 41}, "file:///Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/raft_server.go": {"language": "Go", "code": 190, "comment": 3, "blank": 27}, "file:///Users/<USER>/WorkSpace/tinykv/kv/server/server.go": {"language": "Go", "code": 69, "comment": 17, "blank": 19}, "file:///Users/<USER>/WorkSpace/tinykv/kv/server/server_test.go": {"language": "Go", "code": 294, "comment": 0, "blank": 56}, "file:///Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/resolver.go": {"language": "Go", "code": 55, "comment": 2, "blank": 11}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator/operator_test.go": {"language": "Go", "code": 144, "comment": 15, "blank": 19}, "file:///Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/raft_client.go": {"language": "Go", "code": 115, "comment": 1, "blank": 15}, "file:///Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/region_reader.go": {"language": "Go", "code": 73, "comment": 2, "blank": 16}, "file:///Users/<USER>/WorkSpace/tinykv/kv/storage/raft_storage/snap_runner.go": {"language": "Go", "code": 161, "comment": 0, "blank": 22}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator/operator_kind.go": {"language": "Go", "code": 60, "comment": 15, "blank": 11}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/filter/filters.go": {"language": "Go", "code": 162, "comment": 34, "blank": 39}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator/operator.go": {"language": "Go", "code": 313, "comment": 69, "blank": 53}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/checker_controller.go": {"language": "Go", "code": 31, "comment": 18, "blank": 7}, "file:///Users/<USER>/WorkSpace/tinykv/kv/coprocessor/topn.go": {"language": "Go", "code": 95, "comment": 21, "blank": 24}, "file:///Users/<USER>/WorkSpace/tinykv/kv/coprocessor/analyze.go": {"language": "Go", "code": 269, "comment": 14, "blank": 23}, "file:///Users/<USER>/WorkSpace/tinykv/kv/coprocessor/cop_handler.go": {"language": "Go", "code": 284, "comment": 23, "blank": 34}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/peer_storage_test.go": {"language": "Go", "code": 228, "comment": 4, "blank": 16}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/peer_storage.go": {"language": "Go", "code": 286, "comment": 28, "blank": 34}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/cmd_resp.go": {"language": "Go", "code": 53, "comment": 0, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/bootstrap.go": {"language": "Go", "code": 142, "comment": 1, "blank": 12}, "file:///Users/<USER>/WorkSpace/tinykv/kv/coprocessor/closure_exec.go": {"language": "Go", "code": 658, "comment": 15, "blank": 67}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/store_worker.go": {"language": "Go", "code": 271, "comment": 21, "blank": 25}, "file:///Users/<USER>/WorkSpace/tinykv/kv/coprocessor/rowcodec/common.go": {"language": "Go", "code": 194, "comment": 7, "blank": 20}, "file:///Users/<USER>/WorkSpace/tinykv/kv/coprocessor/rowcodec/encoder.go": {"language": "Go", "code": 262, "comment": 9, "blank": 33}, "file:///Users/<USER>/WorkSpace/tinykv/kv/coprocessor/rowcodec/decoder.go": {"language": "Go", "code": 187, "comment": 13, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/node.go": {"language": "Go", "code": 187, "comment": 0, "blank": 26}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/router.go": {"language": "Go", "code": 84, "comment": 2, "blank": 19}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/ticker.go": {"language": "Go", "code": 110, "comment": 3, "blank": 18}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/raft_worker.go": {"language": "Go", "code": 57, "comment": 7, "blank": 9}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/peer.go": {"language": "Go", "code": 279, "comment": 64, "blank": 50}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/raftstore.go": {"language": "Go", "code": 273, "comment": 15, "blank": 27}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/snap/snap_builder.go": {"language": "Go", "code": 53, "comment": 1, "blank": 7}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/bootstrap_test.go": {"language": "Go", "code": 33, "comment": 0, "blank": 6}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/util/error_test.go": {"language": "Go", "code": 38, "comment": 0, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/snap/snap_test.go": {"language": "Go", "code": 198, "comment": 12, "blank": 32}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/util/util.go": {"language": "Go", "code": 150, "comment": 29, "blank": 29}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/message/msg.go": {"language": "Go", "code": 43, "comment": 17, "blank": 11}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/util/test_util.go": {"language": "Go", "code": 35, "comment": 0, "blank": 4}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/message/callback.go": {"language": "Go", "code": 39, "comment": 0, "blank": 8}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/util/util_test.go": {"language": "Go", "code": 165, "comment": 7, "blank": 22}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/util/error.go": {"language": "Go", "code": 66, "comment": 0, "blank": 16}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/snap/snap.go": {"language": "Go", "code": 696, "comment": 31, "blank": 60}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/runner/split_checker.go": {"language": "Go", "code": 114, "comment": 6, "blank": 15}, "file:///Users/<USER>/WorkSpace/tinykv/kv/config/config.go": {"language": "Go", "code": 84, "comment": 10, "blank": 17}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/message/raft_router.go": {"language": "Go", "code": 10, "comment": 0, "blank": 3}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/peer_msg_handler.go": {"language": "Go", "code": 474, "comment": 54, "blank": 46}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/meta/keys.go": {"language": "Go", "code": 94, "comment": 10, "blank": 20}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/opt/opts.go": {"language": "Go", "code": 20, "comment": 17, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/scheduler_client/client.go": {"language": "Go", "code": 478, "comment": 16, "blank": 62}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/meta/values.go": {"language": "Go", "code": 87, "comment": 3, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/runner/runner_test.go": {"language": "Go", "code": 241, "comment": 7, "blank": 31}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/runner/region_task.go": {"language": "Go", "code": 170, "comment": 12, "blank": 28}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/runner/raftlog_gc.go": {"language": "Go", "code": 78, "comment": 2, "blank": 11}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator_controller.go": {"language": "Go", "code": 274, "comment": 47, "blank": 43}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/test_util.go": {"language": "Go", "code": 49, "comment": 14, "blank": 5}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator_controller_test.go": {"language": "Go", "code": 98, "comment": 18, "blank": 19}, "file:///Users/<USER>/WorkSpace/tinykv/kv/util/worker/worker.go": {"language": "Go", "code": 49, "comment": 0, "blank": 12}, "file:///Users/<USER>/WorkSpace/tinykv/proto/pkg/coprocessor/coprocessor.pb.go": {"language": "Go", "code": 1086, "comment": 9, "blank": 46}, "file:///Users/<USER>/WorkSpace/tinykv/proto/pkg/raft_cmdpb/raft_cmdpb.pb.go": {"language": "Go", "code": 5572, "comment": 18, "blank": 240}, "file:///Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/write_batch.go": {"language": "Go", "code": 96, "comment": 0, "blank": 15}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/runner/scheduler_task.go": {"language": "Go", "code": 133, "comment": 0, "blank": 20}, "file:///Users/<USER>/WorkSpace/tinykv/proto/pkg/tinykvpb/tinykvpb.pb.go": {"language": "Go", "code": 559, "comment": 21, "blank": 68}, "file:///Users/<USER>/WorkSpace/tinykv/proto/pkg/metapb/metapb.pb.go": {"language": "Go", "code": 1318, "comment": 14, "blank": 64}, "file:///Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/engines.go": {"language": "Go", "code": 66, "comment": 8, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/doc.go": {"language": "Go", "code": 1, "comment": 15, "blank": 2}, "file:///Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/cf_iterator.go": {"language": "Go", "code": 87, "comment": 17, "blank": 26}, "file:///Users/<USER>/WorkSpace/tinykv/kv/raftstore/snap/snap_manager.go": {"language": "Go", "code": 320, "comment": 1, "blank": 25}, "file:///Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/util.go": {"language": "Go", "code": 96, "comment": 0, "blank": 15}, "file:///Users/<USER>/WorkSpace/tinykv/kv/util/file.go": {"language": "Go", "code": 49, "comment": 1, "blank": 8}, "file:///Users/<USER>/WorkSpace/tinykv/kv/util/engine_util/engine_util_test.go": {"language": "Go", "code": 84, "comment": 0, "blank": 10}, "file:///Users/<USER>/WorkSpace/tinykv/proto/generate_go.sh": {"language": "<PERSON> Script", "code": 55, "comment": 3, "blank": 14}, "file:///Users/<USER>/WorkSpace/tinykv/proto/pkg/kvrpcpb/kvrpcpb.pb.go": {"language": "Go", "code": 7685, "comment": 62, "blank": 308}, "file:///Users/<USER>/WorkSpace/tinykv/proto/pkg/raft_serverpb/raft_serverpb.pb.go": {"language": "Go", "code": 2964, "comment": 24, "blank": 130}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/checker/replica_checker.go": {"language": "Go", "code": 153, "comment": 28, "blank": 24}, "file:///Users/<USER>/WorkSpace/tinykv/kv/util/codec/codec.go": {"language": "Go", "code": 56, "comment": 17, "blank": 12}, "file:///Users/<USER>/WorkSpace/tinykv/proto/pkg/schedulerpb/schedulerpb.pb.go": {"language": "Go", "code": 13305, "comment": 65, "blank": 586}, "file:///Users/<USER>/WorkSpace/tinykv/proto/pkg/eraftpb/eraftpb.pb.go": {"language": "Go", "code": 2144, "comment": 43, "blank": 97}, "file:///Users/<USER>/WorkSpace/tinykv/proto/pkg/errorpb/errorpb.pb.go": {"language": "Go", "code": 1767, "comment": 8, "blank": 78}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/selector/selector.go": {"language": "Go", "code": 89, "comment": 27, "blank": 14}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/selector/selector_test.go": {"language": "Go", "code": 26, "comment": 12, "blank": 11}, "file:///Users/<USER>/WorkSpace/tinykv/scheduler/server/schedule/operator_queue.go": {"language": "Go", "code": 31, "comment": 12, "blank": 11}}