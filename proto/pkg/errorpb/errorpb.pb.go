// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: errorpb.proto

package errorpb

import (
	"fmt"
	"io"
	"math"

	proto "github.com/golang/protobuf/proto"

	_ "github.com/gogo/protobuf/gogoproto"

	metapb "github.com/pingcap-incubator/tinykv/proto/pkg/metapb"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type NotLeader struct {
	RegionId             uint64       `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	Leader               *metapb.Peer `protobuf:"bytes,2,opt,name=leader" json:"leader,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *NotLeader) Reset()         { *m = NotLeader{} }
func (m *NotLeader) String() string { return proto.CompactTextString(m) }
func (*NotLeader) ProtoMessage()    {}
func (*NotLeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_errorpb_6ea187258f91197d, []int{0}
}
func (m *NotLeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *NotLeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_NotLeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *NotLeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotLeader.Merge(dst, src)
}
func (m *NotLeader) XXX_Size() int {
	return m.Size()
}
func (m *NotLeader) XXX_DiscardUnknown() {
	xxx_messageInfo_NotLeader.DiscardUnknown(m)
}

var xxx_messageInfo_NotLeader proto.InternalMessageInfo

func (m *NotLeader) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *NotLeader) GetLeader() *metapb.Peer {
	if m != nil {
		return m.Leader
	}
	return nil
}

type StoreNotMatch struct {
	RequestStoreId       uint64   `protobuf:"varint,1,opt,name=request_store_id,json=requestStoreId,proto3" json:"request_store_id,omitempty"`
	ActualStoreId        uint64   `protobuf:"varint,2,opt,name=actual_store_id,json=actualStoreId,proto3" json:"actual_store_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreNotMatch) Reset()         { *m = StoreNotMatch{} }
func (m *StoreNotMatch) String() string { return proto.CompactTextString(m) }
func (*StoreNotMatch) ProtoMessage()    {}
func (*StoreNotMatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_errorpb_6ea187258f91197d, []int{1}
}
func (m *StoreNotMatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreNotMatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreNotMatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *StoreNotMatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreNotMatch.Merge(dst, src)
}
func (m *StoreNotMatch) XXX_Size() int {
	return m.Size()
}
func (m *StoreNotMatch) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreNotMatch.DiscardUnknown(m)
}

var xxx_messageInfo_StoreNotMatch proto.InternalMessageInfo

func (m *StoreNotMatch) GetRequestStoreId() uint64 {
	if m != nil {
		return m.RequestStoreId
	}
	return 0
}

func (m *StoreNotMatch) GetActualStoreId() uint64 {
	if m != nil {
		return m.ActualStoreId
	}
	return 0
}

type RegionNotFound struct {
	RegionId             uint64   `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegionNotFound) Reset()         { *m = RegionNotFound{} }
func (m *RegionNotFound) String() string { return proto.CompactTextString(m) }
func (*RegionNotFound) ProtoMessage()    {}
func (*RegionNotFound) Descriptor() ([]byte, []int) {
	return fileDescriptor_errorpb_6ea187258f91197d, []int{2}
}
func (m *RegionNotFound) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionNotFound) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionNotFound.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RegionNotFound) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionNotFound.Merge(dst, src)
}
func (m *RegionNotFound) XXX_Size() int {
	return m.Size()
}
func (m *RegionNotFound) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionNotFound.DiscardUnknown(m)
}

var xxx_messageInfo_RegionNotFound proto.InternalMessageInfo

func (m *RegionNotFound) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type KeyNotInRegion struct {
	Key                  []byte   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	RegionId             uint64   `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	StartKey             []byte   `protobuf:"bytes,3,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey               []byte   `protobuf:"bytes,4,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KeyNotInRegion) Reset()         { *m = KeyNotInRegion{} }
func (m *KeyNotInRegion) String() string { return proto.CompactTextString(m) }
func (*KeyNotInRegion) ProtoMessage()    {}
func (*KeyNotInRegion) Descriptor() ([]byte, []int) {
	return fileDescriptor_errorpb_6ea187258f91197d, []int{3}
}
func (m *KeyNotInRegion) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KeyNotInRegion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KeyNotInRegion.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *KeyNotInRegion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeyNotInRegion.Merge(dst, src)
}
func (m *KeyNotInRegion) XXX_Size() int {
	return m.Size()
}
func (m *KeyNotInRegion) XXX_DiscardUnknown() {
	xxx_messageInfo_KeyNotInRegion.DiscardUnknown(m)
}

var xxx_messageInfo_KeyNotInRegion proto.InternalMessageInfo

func (m *KeyNotInRegion) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *KeyNotInRegion) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *KeyNotInRegion) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *KeyNotInRegion) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

type EpochNotMatch struct {
	CurrentRegions       []*metapb.Region `protobuf:"bytes,1,rep,name=current_regions,json=currentRegions" json:"current_regions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *EpochNotMatch) Reset()         { *m = EpochNotMatch{} }
func (m *EpochNotMatch) String() string { return proto.CompactTextString(m) }
func (*EpochNotMatch) ProtoMessage()    {}
func (*EpochNotMatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_errorpb_6ea187258f91197d, []int{4}
}
func (m *EpochNotMatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EpochNotMatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EpochNotMatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *EpochNotMatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EpochNotMatch.Merge(dst, src)
}
func (m *EpochNotMatch) XXX_Size() int {
	return m.Size()
}
func (m *EpochNotMatch) XXX_DiscardUnknown() {
	xxx_messageInfo_EpochNotMatch.DiscardUnknown(m)
}

var xxx_messageInfo_EpochNotMatch proto.InternalMessageInfo

func (m *EpochNotMatch) GetCurrentRegions() []*metapb.Region {
	if m != nil {
		return m.CurrentRegions
	}
	return nil
}

type StaleCommand struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StaleCommand) Reset()         { *m = StaleCommand{} }
func (m *StaleCommand) String() string { return proto.CompactTextString(m) }
func (*StaleCommand) ProtoMessage()    {}
func (*StaleCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_errorpb_6ea187258f91197d, []int{5}
}
func (m *StaleCommand) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StaleCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StaleCommand.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *StaleCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StaleCommand.Merge(dst, src)
}
func (m *StaleCommand) XXX_Size() int {
	return m.Size()
}
func (m *StaleCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_StaleCommand.DiscardUnknown(m)
}

var xxx_messageInfo_StaleCommand proto.InternalMessageInfo

type Error struct {
	Message              string          `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	NotLeader            *NotLeader      `protobuf:"bytes,2,opt,name=not_leader,json=notLeader" json:"not_leader,omitempty"`
	RegionNotFound       *RegionNotFound `protobuf:"bytes,3,opt,name=region_not_found,json=regionNotFound" json:"region_not_found,omitempty"`
	KeyNotInRegion       *KeyNotInRegion `protobuf:"bytes,4,opt,name=key_not_in_region,json=keyNotInRegion" json:"key_not_in_region,omitempty"`
	EpochNotMatch        *EpochNotMatch  `protobuf:"bytes,5,opt,name=epoch_not_match,json=epochNotMatch" json:"epoch_not_match,omitempty"`
	StaleCommand         *StaleCommand   `protobuf:"bytes,7,opt,name=stale_command,json=staleCommand" json:"stale_command,omitempty"`
	StoreNotMatch        *StoreNotMatch  `protobuf:"bytes,8,opt,name=store_not_match,json=storeNotMatch" json:"store_not_match,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_errorpb_6ea187258f91197d, []int{6}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(dst, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *Error) GetNotLeader() *NotLeader {
	if m != nil {
		return m.NotLeader
	}
	return nil
}

func (m *Error) GetRegionNotFound() *RegionNotFound {
	if m != nil {
		return m.RegionNotFound
	}
	return nil
}

func (m *Error) GetKeyNotInRegion() *KeyNotInRegion {
	if m != nil {
		return m.KeyNotInRegion
	}
	return nil
}

func (m *Error) GetEpochNotMatch() *EpochNotMatch {
	if m != nil {
		return m.EpochNotMatch
	}
	return nil
}

func (m *Error) GetStaleCommand() *StaleCommand {
	if m != nil {
		return m.StaleCommand
	}
	return nil
}

func (m *Error) GetStoreNotMatch() *StoreNotMatch {
	if m != nil {
		return m.StoreNotMatch
	}
	return nil
}

func init() {
	proto.RegisterType((*NotLeader)(nil), "errorpb.NotLeader")
	proto.RegisterType((*StoreNotMatch)(nil), "errorpb.StoreNotMatch")
	proto.RegisterType((*RegionNotFound)(nil), "errorpb.RegionNotFound")
	proto.RegisterType((*KeyNotInRegion)(nil), "errorpb.KeyNotInRegion")
	proto.RegisterType((*EpochNotMatch)(nil), "errorpb.EpochNotMatch")
	proto.RegisterType((*StaleCommand)(nil), "errorpb.StaleCommand")
	proto.RegisterType((*Error)(nil), "errorpb.Error")
}
func (m *NotLeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotLeader) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
	}
	if m.Leader != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.Leader.Size()))
		n1, err := m.Leader.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *StoreNotMatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreNotMatch) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RequestStoreId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RequestStoreId))
	}
	if m.ActualStoreId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.ActualStoreId))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RegionNotFound) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionNotFound) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *KeyNotInRegion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KeyNotInRegion) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Key) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.Key)))
		i += copy(dAtA[i:], m.Key)
	}
	if m.RegionId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
	}
	if len(m.StartKey) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.StartKey)))
		i += copy(dAtA[i:], m.StartKey)
	}
	if len(m.EndKey) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.EndKey)))
		i += copy(dAtA[i:], m.EndKey)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *EpochNotMatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EpochNotMatch) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CurrentRegions) > 0 {
		for _, msg := range m.CurrentRegions {
			dAtA[i] = 0xa
			i++
			i = encodeVarintErrorpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *StaleCommand) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StaleCommand) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Message) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.Message)))
		i += copy(dAtA[i:], m.Message)
	}
	if m.NotLeader != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.NotLeader.Size()))
		n2, err := m.NotLeader.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.RegionNotFound != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionNotFound.Size()))
		n3, err := m.RegionNotFound.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.KeyNotInRegion != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.KeyNotInRegion.Size()))
		n4, err := m.KeyNotInRegion.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.EpochNotMatch != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.EpochNotMatch.Size()))
		n5, err := m.EpochNotMatch.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.StaleCommand != nil {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.StaleCommand.Size()))
		n6, err := m.StaleCommand.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.StoreNotMatch != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintErrorpb(dAtA, i, uint64(m.StoreNotMatch.Size()))
		n7, err := m.StoreNotMatch.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func encodeVarintErrorpb(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *NotLeader) Size() (n int) {
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	if m.Leader != nil {
		l = m.Leader.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StoreNotMatch) Size() (n int) {
	var l int
	_ = l
	if m.RequestStoreId != 0 {
		n += 1 + sovErrorpb(uint64(m.RequestStoreId))
	}
	if m.ActualStoreId != 0 {
		n += 1 + sovErrorpb(uint64(m.ActualStoreId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RegionNotFound) Size() (n int) {
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *KeyNotInRegion) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *EpochNotMatch) Size() (n int) {
	var l int
	_ = l
	if len(m.CurrentRegions) > 0 {
		for _, e := range m.CurrentRegions {
			l = e.Size()
			n += 1 + l + sovErrorpb(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StaleCommand) Size() (n int) {
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Error) Size() (n int) {
	var l int
	_ = l
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.NotLeader != nil {
		l = m.NotLeader.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.RegionNotFound != nil {
		l = m.RegionNotFound.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.KeyNotInRegion != nil {
		l = m.KeyNotInRegion.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.EpochNotMatch != nil {
		l = m.EpochNotMatch.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.StaleCommand != nil {
		l = m.StaleCommand.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.StoreNotMatch != nil {
		l = m.StoreNotMatch.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovErrorpb(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozErrorpb(x uint64) (n int) {
	return sovErrorpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *NotLeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NotLeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NotLeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Leader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Leader == nil {
				m.Leader = &metapb.Peer{}
			}
			if err := m.Leader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreNotMatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreNotMatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreNotMatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestStoreId", wireType)
			}
			m.RequestStoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestStoreId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActualStoreId", wireType)
			}
			m.ActualStoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActualStoreId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionNotFound) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionNotFound: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionNotFound: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KeyNotInRegion) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KeyNotInRegion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KeyNotInRegion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EpochNotMatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EpochNotMatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EpochNotMatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentRegions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CurrentRegions = append(m.CurrentRegions, &metapb.Region{})
			if err := m.CurrentRegions[len(m.CurrentRegions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StaleCommand) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StaleCommand: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StaleCommand: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NotLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.NotLeader == nil {
				m.NotLeader = &NotLeader{}
			}
			if err := m.NotLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionNotFound", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionNotFound == nil {
				m.RegionNotFound = &RegionNotFound{}
			}
			if err := m.RegionNotFound.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyNotInRegion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.KeyNotInRegion == nil {
				m.KeyNotInRegion = &KeyNotInRegion{}
			}
			if err := m.KeyNotInRegion.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EpochNotMatch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.EpochNotMatch == nil {
				m.EpochNotMatch = &EpochNotMatch{}
			}
			if err := m.EpochNotMatch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StaleCommand", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StaleCommand == nil {
				m.StaleCommand = &StaleCommand{}
			}
			if err := m.StaleCommand.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreNotMatch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StoreNotMatch == nil {
				m.StoreNotMatch = &StoreNotMatch{}
			}
			if err := m.StoreNotMatch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipErrorpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthErrorpb
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowErrorpb
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipErrorpb(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthErrorpb = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowErrorpb   = fmt.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("errorpb.proto", fileDescriptor_errorpb_6ea187258f91197d) }

var fileDescriptor_errorpb_6ea187258f91197d = []byte{
	// 499 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x53, 0xcd, 0x8e, 0x12, 0x4d,
	0x14, 0xfd, 0x1a, 0x66, 0xf8, 0xb9, 0xd0, 0x0d, 0x5f, 0x47, 0x9d, 0xce, 0x4c, 0x42, 0x48, 0xc7,
	0x18, 0x36, 0x62, 0xc4, 0x85, 0x89, 0x0b, 0x13, 0xc7, 0x8c, 0x91, 0xa0, 0xc4, 0xd4, 0x3c, 0x40,
	0xa7, 0x86, 0xbe, 0x32, 0x04, 0xa8, 0xc2, 0xaa, 0x62, 0xc1, 0x9b, 0xf8, 0x00, 0x3e, 0x8c, 0x4b,
	0x1f, 0xc1, 0xe0, 0x8b, 0x98, 0xba, 0xd5, 0x34, 0xd4, 0x2c, 0xdc, 0xf5, 0xfd, 0x39, 0xe7, 0xde,
	0x7b, 0x4e, 0x35, 0x84, 0xa8, 0x94, 0x54, 0x9b, 0xbb, 0xe1, 0x46, 0x49, 0x23, 0xe3, 0x7a, 0x11,
	0x5e, 0xb6, 0xd7, 0x68, 0xf8, 0x21, 0x7d, 0xf9, 0x68, 0x2e, 0xe7, 0x92, 0x3e, 0x5f, 0xd8, 0x2f,
	0x97, 0x4d, 0xa7, 0xd0, 0x9c, 0x4a, 0xf3, 0x09, 0x79, 0x8e, 0x2a, 0xbe, 0x82, 0xa6, 0xc2, 0xf9,
	0x42, 0x8a, 0x6c, 0x91, 0x27, 0x41, 0x3f, 0x18, 0x9c, 0xb1, 0x86, 0x4b, 0x8c, 0xf3, 0xf8, 0x29,
	0xd4, 0x56, 0xd4, 0x96, 0x54, 0xfa, 0xc1, 0xa0, 0x35, 0x6a, 0x0f, 0x0b, 0xfa, 0x2f, 0x88, 0x8a,
	0x15, 0xb5, 0x94, 0x43, 0x78, 0x6b, 0xa4, 0xc2, 0xa9, 0x34, 0x9f, 0xb9, 0x99, 0xdd, 0xc7, 0x03,
	0xe8, 0x2a, 0xfc, 0xb6, 0x45, 0x6d, 0x32, 0x6d, 0x0b, 0x47, 0xea, 0xa8, 0xc8, 0x53, 0xff, 0x38,
	0x8f, 0x9f, 0x41, 0x87, 0xcf, 0xcc, 0x96, 0xaf, 0x8e, 0x8d, 0x15, 0x6a, 0x0c, 0x5d, 0xba, 0xe8,
	0x4b, 0x9f, 0x43, 0xc4, 0x68, 0xa9, 0xa9, 0x34, 0x1f, 0xe4, 0x56, 0xe4, 0xff, 0xdc, 0x3b, 0xdd,
	0x42, 0x34, 0xc1, 0xdd, 0x54, 0x9a, 0xb1, 0x70, 0xb0, 0xb8, 0x0b, 0xd5, 0x25, 0xee, 0xa8, 0xb1,
	0xcd, 0xec, 0xa7, 0x4f, 0x50, 0x79, 0x70, 0xf8, 0x15, 0x34, 0xb5, 0xe1, 0xca, 0x64, 0x16, 0x54,
	0x25, 0x50, 0x83, 0x12, 0x13, 0xdc, 0xc5, 0x17, 0x50, 0x47, 0x91, 0x53, 0xe9, 0x8c, 0x4a, 0x35,
	0x14, 0xf9, 0x04, 0x77, 0xe9, 0x47, 0x08, 0x6f, 0x36, 0x72, 0x76, 0x5f, 0x0a, 0xf1, 0x1a, 0x3a,
	0xb3, 0xad, 0x52, 0x28, 0x4c, 0xe6, 0xa8, 0x75, 0x12, 0xf4, 0xab, 0x83, 0xd6, 0x28, 0x3a, 0x08,
	0xe9, 0xd6, 0x63, 0x51, 0xd1, 0xe6, 0x42, 0x9d, 0x46, 0xd0, 0xbe, 0x35, 0x7c, 0x85, 0xef, 0xe5,
	0x7a, 0xcd, 0x45, 0x9e, 0xfe, 0xa8, 0xc2, 0xf9, 0x8d, 0xb5, 0x38, 0x4e, 0xa0, 0xbe, 0x46, 0xad,
	0xf9, 0x1c, 0xe9, 0x98, 0x26, 0x3b, 0x84, 0xf1, 0x4b, 0x00, 0x21, 0x4d, 0xe6, 0x19, 0x16, 0x0f,
	0x0f, 0xef, 0xa4, 0x74, 0x9c, 0x35, 0x45, 0x69, 0xfe, 0x3b, 0x6b, 0x14, 0x69, 0x60, 0x91, 0x5f,
	0xad, 0xb0, 0x74, 0x6d, 0x6b, 0x74, 0x51, 0x02, 0x7d, 0xdd, 0xad, 0x83, 0x9e, 0x0f, 0xd7, 0xf0,
	0xff, 0x12, 0x77, 0x84, 0x5f, 0x88, 0xe2, 0x4a, 0x92, 0xe5, 0x94, 0xc3, 0x37, 0x83, 0x45, 0x4b,
	0xdf, 0x9c, 0xb7, 0xd0, 0x41, 0xab, 0x1b, 0xb1, 0xac, 0xad, 0x72, 0xc9, 0x39, 0x31, 0x3c, 0x29,
	0x19, 0x3c, 0x5d, 0x59, 0x88, 0x9e, 0xcc, 0x6f, 0x20, 0xd4, 0x56, 0xad, 0x6c, 0xe6, 0xe4, 0x4a,
	0xea, 0x84, 0x7e, 0x5c, 0xa2, 0x4f, 0xb5, 0x64, 0x6d, 0x7d, 0x12, 0xd9, 0xd9, 0xee, 0xe9, 0x1d,
	0x67, 0x37, 0x1e, 0xcc, 0xf6, 0x1e, 0x37, 0x0b, 0xb5, 0x17, 0xb6, 0xdc, 0x64, 0x5a, 0xe8, 0xba,
	0xfb, 0x73, 0xdf, 0x0b, 0x7e, 0xed, 0x7b, 0xc1, 0xef, 0x7d, 0x2f, 0xf8, 0xfe, 0xa7, 0xf7, 0xdf,
	0x5d, 0x8d, 0x7e, 0xb9, 0x57, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x26, 0x03, 0xaa, 0x44, 0xb0,
	0x03, 0x00, 0x00,
}
