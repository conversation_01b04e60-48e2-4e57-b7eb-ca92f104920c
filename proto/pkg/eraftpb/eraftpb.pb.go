// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: eraftpb.proto

package eraftpb

import (
	"fmt"
	"io"
	"math"

	proto "github.com/golang/protobuf/proto"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EntryType int32

const (
	EntryType_EntryNormal     EntryType = 0
	EntryType_EntryConfChange EntryType = 1
)

var EntryType_name = map[int32]string{
	0: "EntryNormal",
	1: "EntryConfChange",
}
var EntryType_value = map[string]int32{
	"EntryNormal":     0,
	"EntryConfChange": 1,
}

func (x EntryType) String() string {
	return proto.EnumName(EntryType_name, int32(x))
}
func (EntryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_eraftpb_2f2e0bcef614736b, []int{0}
}

// Some MessageType defined here are local messages which not come from the network, but should
// also use the Step method to handle
type MessageType int32

const (
	// 'MessageType_MsgHup' is a local message used for election. If an election timeout happened,
	// the node should pass 'MessageType_MsgHup' to its Step method and start a new election.
	MessageType_MsgHup MessageType = 0
	// 'MessageType_MsgBeat' is a local message that signals the leader to send a heartbeat
	// of the 'MessageType_MsgHeartbeat' type to its followers.
	MessageType_MsgBeat MessageType = 1
	// 'MessageType_MsgPropose' is a local message that proposes to append data to the leader's log entries.
	MessageType_MsgPropose MessageType = 2
	// 'MessageType_MsgAppend' contains log entries to replicate.
	MessageType_MsgAppend MessageType = 3
	// 'MessageType_MsgAppendResponse' is response to log replication request('MessageType_MsgAppend').
	MessageType_MsgAppendResponse MessageType = 4
	// 'MessageType_MsgRequestVote' requests votes for election.
	MessageType_MsgRequestVote MessageType = 5
	// 'MessageType_MsgRequestVoteResponse' contains responses from voting request.
	MessageType_MsgRequestVoteResponse MessageType = 6
	// 'MessageType_MsgSnapshot' requests to install a snapshot message.
	MessageType_MsgSnapshot MessageType = 7
	// 'MessageType_MsgHeartbeat' sends heartbeat from leader to its followers.
	MessageType_MsgHeartbeat MessageType = 8
	// 'MessageType_MsgHeartbeatResponse' is a response to 'MessageType_MsgHeartbeat'.
	MessageType_MsgHeartbeatResponse MessageType = 9
	// 'MessageType_MsgTransferLeader' requests the leader to transfer its leadership.
	MessageType_MsgTransferLeader MessageType = 11
	// 'MessageType_MsgTimeoutNow' send from the leader to the leadership transfer target, to let
	// the transfer target timeout immediately and start a new election.
	MessageType_MsgTimeoutNow MessageType = 12
)

var MessageType_name = map[int32]string{
	0:  "MsgHup",
	1:  "MsgBeat",
	2:  "MsgPropose",
	3:  "MsgAppend",
	4:  "MsgAppendResponse",
	5:  "MsgRequestVote",
	6:  "MsgRequestVoteResponse",
	7:  "MsgSnapshot",
	8:  "MsgHeartbeat",
	9:  "MsgHeartbeatResponse",
	11: "MsgTransferLeader",
	12: "MsgTimeoutNow",
}
var MessageType_value = map[string]int32{
	"MsgHup":                 0,
	"MsgBeat":                1,
	"MsgPropose":             2,
	"MsgAppend":              3,
	"MsgAppendResponse":      4,
	"MsgRequestVote":         5,
	"MsgRequestVoteResponse": 6,
	"MsgSnapshot":            7,
	"MsgHeartbeat":           8,
	"MsgHeartbeatResponse":   9,
	"MsgTransferLeader":      11,
	"MsgTimeoutNow":          12,
}

func (x MessageType) String() string {
	return proto.EnumName(MessageType_name, int32(x))
}
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_eraftpb_2f2e0bcef614736b, []int{1}
}

type ConfChangeType int32

const (
	ConfChangeType_AddNode    ConfChangeType = 0
	ConfChangeType_RemoveNode ConfChangeType = 1
)

var ConfChangeType_name = map[int32]string{
	0: "AddNode",
	1: "RemoveNode",
}
var ConfChangeType_value = map[string]int32{
	"AddNode":    0,
	"RemoveNode": 1,
}

func (x ConfChangeType) String() string {
	return proto.EnumName(ConfChangeType_name, int32(x))
}
func (ConfChangeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_eraftpb_2f2e0bcef614736b, []int{2}
}

// The entry is a type of change that needs to be applied. It contains two data fields.
// While the fields are built into the model; their usage is determined by the entry_type.
//
// For normal entries, the data field should contain the data change that should be applied.
// The context field can be used for any contextual data that might be relevant to the
// application of the data.
//
// For configuration changes, the data will contain the ConfChange message and the
// context will provide anything needed to assist the configuration change. The context
// is for the user to set and use in this case.
type Entry struct {
	EntryType            EntryType `protobuf:"varint,1,opt,name=entry_type,json=entryType,proto3,enum=eraftpb.EntryType" json:"entry_type,omitempty"`
	Term                 uint64    `protobuf:"varint,2,opt,name=term,proto3" json:"term,omitempty"`
	Index                uint64    `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`
	Data                 []byte    `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *Entry) Reset()         { *m = Entry{} }
func (m *Entry) String() string { return proto.CompactTextString(m) }
func (*Entry) ProtoMessage()    {}
func (*Entry) Descriptor() ([]byte, []int) {
	return fileDescriptor_eraftpb_2f2e0bcef614736b, []int{0}
}
func (m *Entry) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Entry) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Entry.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *Entry) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Entry.Merge(dst, src)
}
func (m *Entry) XXX_Size() int {
	return m.Size()
}
func (m *Entry) XXX_DiscardUnknown() {
	xxx_messageInfo_Entry.DiscardUnknown(m)
}

var xxx_messageInfo_Entry proto.InternalMessageInfo

func (m *Entry) GetEntryType() EntryType {
	if m != nil {
		return m.EntryType
	}
	return EntryType_EntryNormal
}

func (m *Entry) GetTerm() uint64 {
	if m != nil {
		return m.Term
	}
	return 0
}

func (m *Entry) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *Entry) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

// SnapshotMetadata contains the log index and term of the last log applied to this
// Snapshot, along with the membership information of the time the last log applied.
type SnapshotMetadata struct {
	ConfState            *ConfState `protobuf:"bytes,1,opt,name=conf_state,json=confState" json:"conf_state,omitempty"`
	Index                uint64     `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	Term                 uint64     `protobuf:"varint,3,opt,name=term,proto3" json:"term,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *SnapshotMetadata) Reset()         { *m = SnapshotMetadata{} }
func (m *SnapshotMetadata) String() string { return proto.CompactTextString(m) }
func (*SnapshotMetadata) ProtoMessage()    {}
func (*SnapshotMetadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_eraftpb_2f2e0bcef614736b, []int{1}
}
func (m *SnapshotMetadata) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotMetadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotMetadata.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *SnapshotMetadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotMetadata.Merge(dst, src)
}
func (m *SnapshotMetadata) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotMetadata) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotMetadata.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotMetadata proto.InternalMessageInfo

func (m *SnapshotMetadata) GetConfState() *ConfState {
	if m != nil {
		return m.ConfState
	}
	return nil
}

func (m *SnapshotMetadata) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *SnapshotMetadata) GetTerm() uint64 {
	if m != nil {
		return m.Term
	}
	return 0
}

type Snapshot struct {
	Data                 []byte            `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Metadata             *SnapshotMetadata `protobuf:"bytes,2,opt,name=metadata" json:"metadata,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Snapshot) Reset()         { *m = Snapshot{} }
func (m *Snapshot) String() string { return proto.CompactTextString(m) }
func (*Snapshot) ProtoMessage()    {}
func (*Snapshot) Descriptor() ([]byte, []int) {
	return fileDescriptor_eraftpb_2f2e0bcef614736b, []int{2}
}
func (m *Snapshot) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Snapshot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Snapshot.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *Snapshot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Snapshot.Merge(dst, src)
}
func (m *Snapshot) XXX_Size() int {
	return m.Size()
}
func (m *Snapshot) XXX_DiscardUnknown() {
	xxx_messageInfo_Snapshot.DiscardUnknown(m)
}

var xxx_messageInfo_Snapshot proto.InternalMessageInfo

func (m *Snapshot) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *Snapshot) GetMetadata() *SnapshotMetadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

type Message struct {
	MsgType              MessageType `protobuf:"varint,1,opt,name=msg_type,json=msgType,proto3,enum=eraftpb.MessageType" json:"msg_type,omitempty"`
	To                   uint64      `protobuf:"varint,2,opt,name=to,proto3" json:"to,omitempty"`
	From                 uint64      `protobuf:"varint,3,opt,name=from,proto3" json:"from,omitempty"`
	Term                 uint64      `protobuf:"varint,4,opt,name=term,proto3" json:"term,omitempty"`
	LogTerm              uint64      `protobuf:"varint,5,opt,name=log_term,json=logTerm,proto3" json:"log_term,omitempty"`
	Index                uint64      `protobuf:"varint,6,opt,name=index,proto3" json:"index,omitempty"`
	Entries              []*Entry    `protobuf:"bytes,7,rep,name=entries" json:"entries,omitempty"`
	Commit               uint64      `protobuf:"varint,8,opt,name=commit,proto3" json:"commit,omitempty"`
	Snapshot             *Snapshot   `protobuf:"bytes,9,opt,name=snapshot" json:"snapshot,omitempty"`
	Reject               bool        `protobuf:"varint,10,opt,name=reject,proto3" json:"reject,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Message) Reset()         { *m = Message{} }
func (m *Message) String() string { return proto.CompactTextString(m) }
func (*Message) ProtoMessage()    {}
func (*Message) Descriptor() ([]byte, []int) {
	return fileDescriptor_eraftpb_2f2e0bcef614736b, []int{3}
}
func (m *Message) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Message) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Message.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *Message) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message.Merge(dst, src)
}
func (m *Message) XXX_Size() int {
	return m.Size()
}
func (m *Message) XXX_DiscardUnknown() {
	xxx_messageInfo_Message.DiscardUnknown(m)
}

var xxx_messageInfo_Message proto.InternalMessageInfo

func (m *Message) GetMsgType() MessageType {
	if m != nil {
		return m.MsgType
	}
	return MessageType_MsgHup
}

func (m *Message) GetTo() uint64 {
	if m != nil {
		return m.To
	}
	return 0
}

func (m *Message) GetFrom() uint64 {
	if m != nil {
		return m.From
	}
	return 0
}

func (m *Message) GetTerm() uint64 {
	if m != nil {
		return m.Term
	}
	return 0
}

func (m *Message) GetLogTerm() uint64 {
	if m != nil {
		return m.LogTerm
	}
	return 0
}

func (m *Message) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *Message) GetEntries() []*Entry {
	if m != nil {
		return m.Entries
	}
	return nil
}

func (m *Message) GetCommit() uint64 {
	if m != nil {
		return m.Commit
	}
	return 0
}

func (m *Message) GetSnapshot() *Snapshot {
	if m != nil {
		return m.Snapshot
	}
	return nil
}

func (m *Message) GetReject() bool {
	if m != nil {
		return m.Reject
	}
	return false
}

// HardState contains the state of a node need to be peristed, including the current term, commit index
// and the vote record
type HardState struct {
	Term                 uint64   `protobuf:"varint,1,opt,name=term,proto3" json:"term,omitempty"`
	Vote                 uint64   `protobuf:"varint,2,opt,name=vote,proto3" json:"vote,omitempty"`
	Commit               uint64   `protobuf:"varint,3,opt,name=commit,proto3" json:"commit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HardState) Reset()         { *m = HardState{} }
func (m *HardState) String() string { return proto.CompactTextString(m) }
func (*HardState) ProtoMessage()    {}
func (*HardState) Descriptor() ([]byte, []int) {
	return fileDescriptor_eraftpb_2f2e0bcef614736b, []int{4}
}
func (m *HardState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *HardState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_HardState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *HardState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HardState.Merge(dst, src)
}
func (m *HardState) XXX_Size() int {
	return m.Size()
}
func (m *HardState) XXX_DiscardUnknown() {
	xxx_messageInfo_HardState.DiscardUnknown(m)
}

var xxx_messageInfo_HardState proto.InternalMessageInfo

func (m *HardState) GetTerm() uint64 {
	if m != nil {
		return m.Term
	}
	return 0
}

func (m *HardState) GetVote() uint64 {
	if m != nil {
		return m.Vote
	}
	return 0
}

func (m *HardState) GetCommit() uint64 {
	if m != nil {
		return m.Commit
	}
	return 0
}

// ConfState contains the current membership information of the raft group
type ConfState struct {
	// all node id
	Nodes                []uint64 `protobuf:"varint,1,rep,packed,name=nodes" json:"nodes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfState) Reset()         { *m = ConfState{} }
func (m *ConfState) String() string { return proto.CompactTextString(m) }
func (*ConfState) ProtoMessage()    {}
func (*ConfState) Descriptor() ([]byte, []int) {
	return fileDescriptor_eraftpb_2f2e0bcef614736b, []int{5}
}
func (m *ConfState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConfState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ConfState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ConfState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfState.Merge(dst, src)
}
func (m *ConfState) XXX_Size() int {
	return m.Size()
}
func (m *ConfState) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfState.DiscardUnknown(m)
}

var xxx_messageInfo_ConfState proto.InternalMessageInfo

func (m *ConfState) GetNodes() []uint64 {
	if m != nil {
		return m.Nodes
	}
	return nil
}

// ConfChange is the data that attach on entry with EntryConfChange type
type ConfChange struct {
	ChangeType ConfChangeType `protobuf:"varint,1,opt,name=change_type,json=changeType,proto3,enum=eraftpb.ConfChangeType" json:"change_type,omitempty"`
	// node will be add/remove
	NodeId               uint64   `protobuf:"varint,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Context              []byte   `protobuf:"bytes,3,opt,name=context,proto3" json:"context,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfChange) Reset()         { *m = ConfChange{} }
func (m *ConfChange) String() string { return proto.CompactTextString(m) }
func (*ConfChange) ProtoMessage()    {}
func (*ConfChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_eraftpb_2f2e0bcef614736b, []int{6}
}
func (m *ConfChange) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ConfChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ConfChange.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ConfChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfChange.Merge(dst, src)
}
func (m *ConfChange) XXX_Size() int {
	return m.Size()
}
func (m *ConfChange) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfChange.DiscardUnknown(m)
}

var xxx_messageInfo_ConfChange proto.InternalMessageInfo

func (m *ConfChange) GetChangeType() ConfChangeType {
	if m != nil {
		return m.ChangeType
	}
	return ConfChangeType_AddNode
}

func (m *ConfChange) GetNodeId() uint64 {
	if m != nil {
		return m.NodeId
	}
	return 0
}

func (m *ConfChange) GetContext() []byte {
	if m != nil {
		return m.Context
	}
	return nil
}

func init() {
	proto.RegisterType((*Entry)(nil), "eraftpb.Entry")
	proto.RegisterType((*SnapshotMetadata)(nil), "eraftpb.SnapshotMetadata")
	proto.RegisterType((*Snapshot)(nil), "eraftpb.Snapshot")
	proto.RegisterType((*Message)(nil), "eraftpb.Message")
	proto.RegisterType((*HardState)(nil), "eraftpb.HardState")
	proto.RegisterType((*ConfState)(nil), "eraftpb.ConfState")
	proto.RegisterType((*ConfChange)(nil), "eraftpb.ConfChange")
	proto.RegisterEnum("eraftpb.EntryType", EntryType_name, EntryType_value)
	proto.RegisterEnum("eraftpb.MessageType", MessageType_name, MessageType_value)
	proto.RegisterEnum("eraftpb.ConfChangeType", ConfChangeType_name, ConfChangeType_value)
}
func (m *Entry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Entry) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.EntryType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.EntryType))
	}
	if m.Term != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Term))
	}
	if m.Index != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Index))
	}
	if len(m.Data) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *SnapshotMetadata) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotMetadata) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ConfState != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.ConfState.Size()))
		n1, err := m.ConfState.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.Index != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Index))
	}
	if m.Term != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Term))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Snapshot) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Snapshot) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Data) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	if m.Metadata != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Metadata.Size()))
		n2, err := m.Metadata.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Message) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Message) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.MsgType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.MsgType))
	}
	if m.To != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.To))
	}
	if m.From != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.From))
	}
	if m.Term != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Term))
	}
	if m.LogTerm != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.LogTerm))
	}
	if m.Index != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Index))
	}
	if len(m.Entries) > 0 {
		for _, msg := range m.Entries {
			dAtA[i] = 0x3a
			i++
			i = encodeVarintEraftpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Commit != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Commit))
	}
	if m.Snapshot != nil {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Snapshot.Size()))
		n3, err := m.Snapshot.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.Reject {
		dAtA[i] = 0x50
		i++
		if m.Reject {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *HardState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HardState) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Term != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Term))
	}
	if m.Vote != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Vote))
	}
	if m.Commit != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.Commit))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ConfState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfState) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Nodes) > 0 {
		dAtA5 := make([]byte, len(m.Nodes)*10)
		var j4 int
		for _, num := range m.Nodes {
			for num >= 1<<7 {
				dAtA5[j4] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j4++
			}
			dAtA5[j4] = uint8(num)
			j4++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(j4))
		i += copy(dAtA[i:], dAtA5[:j4])
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ConfChange) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfChange) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChangeType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.ChangeType))
	}
	if m.NodeId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(m.NodeId))
	}
	if len(m.Context) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintEraftpb(dAtA, i, uint64(len(m.Context)))
		i += copy(dAtA[i:], m.Context)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func encodeVarintEraftpb(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *Entry) Size() (n int) {
	var l int
	_ = l
	if m.EntryType != 0 {
		n += 1 + sovEraftpb(uint64(m.EntryType))
	}
	if m.Term != 0 {
		n += 1 + sovEraftpb(uint64(m.Term))
	}
	if m.Index != 0 {
		n += 1 + sovEraftpb(uint64(m.Index))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovEraftpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SnapshotMetadata) Size() (n int) {
	var l int
	_ = l
	if m.ConfState != nil {
		l = m.ConfState.Size()
		n += 1 + l + sovEraftpb(uint64(l))
	}
	if m.Index != 0 {
		n += 1 + sovEraftpb(uint64(m.Index))
	}
	if m.Term != 0 {
		n += 1 + sovEraftpb(uint64(m.Term))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Snapshot) Size() (n int) {
	var l int
	_ = l
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovEraftpb(uint64(l))
	}
	if m.Metadata != nil {
		l = m.Metadata.Size()
		n += 1 + l + sovEraftpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Message) Size() (n int) {
	var l int
	_ = l
	if m.MsgType != 0 {
		n += 1 + sovEraftpb(uint64(m.MsgType))
	}
	if m.To != 0 {
		n += 1 + sovEraftpb(uint64(m.To))
	}
	if m.From != 0 {
		n += 1 + sovEraftpb(uint64(m.From))
	}
	if m.Term != 0 {
		n += 1 + sovEraftpb(uint64(m.Term))
	}
	if m.LogTerm != 0 {
		n += 1 + sovEraftpb(uint64(m.LogTerm))
	}
	if m.Index != 0 {
		n += 1 + sovEraftpb(uint64(m.Index))
	}
	if len(m.Entries) > 0 {
		for _, e := range m.Entries {
			l = e.Size()
			n += 1 + l + sovEraftpb(uint64(l))
		}
	}
	if m.Commit != 0 {
		n += 1 + sovEraftpb(uint64(m.Commit))
	}
	if m.Snapshot != nil {
		l = m.Snapshot.Size()
		n += 1 + l + sovEraftpb(uint64(l))
	}
	if m.Reject {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *HardState) Size() (n int) {
	var l int
	_ = l
	if m.Term != 0 {
		n += 1 + sovEraftpb(uint64(m.Term))
	}
	if m.Vote != 0 {
		n += 1 + sovEraftpb(uint64(m.Vote))
	}
	if m.Commit != 0 {
		n += 1 + sovEraftpb(uint64(m.Commit))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ConfState) Size() (n int) {
	var l int
	_ = l
	if len(m.Nodes) > 0 {
		l = 0
		for _, e := range m.Nodes {
			l += sovEraftpb(uint64(e))
		}
		n += 1 + sovEraftpb(uint64(l)) + l
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ConfChange) Size() (n int) {
	var l int
	_ = l
	if m.ChangeType != 0 {
		n += 1 + sovEraftpb(uint64(m.ChangeType))
	}
	if m.NodeId != 0 {
		n += 1 + sovEraftpb(uint64(m.NodeId))
	}
	l = len(m.Context)
	if l > 0 {
		n += 1 + l + sovEraftpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovEraftpb(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozEraftpb(x uint64) (n int) {
	return sovEraftpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Entry) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEraftpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Entry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Entry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EntryType", wireType)
			}
			m.EntryType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EntryType |= (EntryType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Term", wireType)
			}
			m.Term = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Term |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEraftpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEraftpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEraftpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotMetadata) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEraftpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotMetadata: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotMetadata: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConfState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEraftpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ConfState == nil {
				m.ConfState = &ConfState{}
			}
			if err := m.ConfState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Term", wireType)
			}
			m.Term = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Term |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEraftpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEraftpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Snapshot) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEraftpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Snapshot: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Snapshot: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEraftpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metadata", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEraftpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Metadata == nil {
				m.Metadata = &SnapshotMetadata{}
			}
			if err := m.Metadata.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEraftpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEraftpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Message) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEraftpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Message: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Message: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MsgType", wireType)
			}
			m.MsgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgType |= (MessageType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field To", wireType)
			}
			m.To = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.To |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			m.From = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.From |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Term", wireType)
			}
			m.Term = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Term |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LogTerm", wireType)
			}
			m.LogTerm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogTerm |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEraftpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Entries = append(m.Entries, &Entry{})
			if err := m.Entries[len(m.Entries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Commit", wireType)
			}
			m.Commit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Commit |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Snapshot", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEraftpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Snapshot == nil {
				m.Snapshot = &Snapshot{}
			}
			if err := m.Snapshot.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reject", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Reject = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipEraftpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEraftpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *HardState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEraftpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: HardState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: HardState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Term", wireType)
			}
			m.Term = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Term |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Vote", wireType)
			}
			m.Vote = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Vote |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Commit", wireType)
			}
			m.Commit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Commit |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEraftpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEraftpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEraftpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConfState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConfState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowEraftpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Nodes = append(m.Nodes, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowEraftpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthEraftpb
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowEraftpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Nodes = append(m.Nodes, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Nodes", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEraftpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEraftpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfChange) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEraftpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ConfChange: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ConfChange: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangeType", wireType)
			}
			m.ChangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeType |= (ConfChangeType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NodeId", wireType)
			}
			m.NodeId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NodeId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEraftpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Context = append(m.Context[:0], dAtA[iNdEx:postIndex]...)
			if m.Context == nil {
				m.Context = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEraftpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthEraftpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipEraftpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowEraftpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEraftpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthEraftpb
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowEraftpb
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipEraftpb(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthEraftpb = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowEraftpb   = fmt.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("eraftpb.proto", fileDescriptor_eraftpb_2f2e0bcef614736b) }

var fileDescriptor_eraftpb_2f2e0bcef614736b = []byte{
	// 666 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x64, 0x94, 0x5f, 0x4e, 0xdb, 0x4e,
	0x10, 0xc7, 0xb3, 0xf9, 0x67, 0x7b, 0x1c, 0xc2, 0xb2, 0x3f, 0x7e, 0x60, 0xfa, 0x10, 0xa5, 0x7e,
	0x8a, 0x90, 0xa0, 0x82, 0xaa, 0x52, 0x5f, 0x01, 0x55, 0xa2, 0x6a, 0x8d, 0x2a, 0x43, 0xfb, 0x1a,
	0x2d, 0xf6, 0xc4, 0xa4, 0xc2, 0x5e, 0xd7, 0xbb, 0x50, 0x72, 0x93, 0x1e, 0xa2, 0x07, 0xe9, 0x63,
	0x8f, 0x50, 0xd1, 0x23, 0xf4, 0x02, 0xd5, 0x6e, 0x6c, 0xc7, 0xa1, 0x6f, 0xf3, 0x1d, 0xcf, 0xee,
	0x7c, 0xe6, 0x3b, 0x9b, 0xc0, 0x06, 0x16, 0x7c, 0xa6, 0xf2, 0xeb, 0xc3, 0xbc, 0x10, 0x4a, 0x30,
	0xab, 0x94, 0xfe, 0x03, 0xf4, 0xde, 0x64, 0xaa, 0x58, 0xb0, 0x23, 0x00, 0xd4, 0xc1, 0x54, 0x2d,
	0x72, 0xf4, 0xc8, 0x98, 0x4c, 0x86, 0xc7, 0xec, 0xb0, 0x3a, 0x65, 0x6a, 0xae, 0x16, 0x39, 0x86,
	0x0e, 0x56, 0x21, 0x63, 0xd0, 0x55, 0x58, 0xa4, 0x5e, 0x7b, 0x4c, 0x26, 0xdd, 0xd0, 0xc4, 0x6c,
	0x1b, 0x7a, 0xf3, 0x2c, 0xc6, 0x07, 0xaf, 0x63, 0x92, 0x4b, 0xa1, 0x2b, 0x63, 0xae, 0xb8, 0xd7,
	0x1d, 0x93, 0xc9, 0x20, 0x34, 0xb1, 0x2f, 0x80, 0x5e, 0x66, 0x3c, 0x97, 0x37, 0x42, 0x05, 0xa8,
	0xb8, 0xce, 0x69, 0x88, 0x48, 0x64, 0xb3, 0xa9, 0x54, 0x5c, 0x2d, 0x21, 0xdc, 0x06, 0xc4, 0x99,
	0xc8, 0x66, 0x97, 0xfa, 0x4b, 0xe8, 0x44, 0x55, 0xb8, 0x6a, 0xd8, 0x7e, 0xd2, 0xd0, 0xa0, 0x75,
	0x56, 0x68, 0xfe, 0x47, 0xb0, 0xab, 0x86, 0x35, 0x10, 0x59, 0x01, 0xb1, 0x57, 0x60, 0xa7, 0x25,
	0x88, 0xb9, 0xcc, 0x3d, 0xde, 0xab, 0x5b, 0x3f, 0x25, 0x0d, 0xeb, 0x52, 0xff, 0x7b, 0x1b, 0xac,
	0x00, 0xa5, 0xe4, 0x09, 0xb2, 0x17, 0x60, 0xa7, 0x32, 0x69, 0x5a, 0xb8, 0x5d, 0x5f, 0x51, 0xd6,
	0x18, 0x13, 0xad, 0x54, 0x26, 0xc6, 0xc2, 0x21, 0xb4, 0x95, 0x28, 0xd1, 0xdb, 0x4a, 0x68, 0xae,
	0x59, 0x21, 0x6a, 0x6e, 0x1d, 0xd7, 0xb3, 0x74, 0x1b, 0x36, 0xef, 0x81, 0x7d, 0x2b, 0x92, 0xa9,
	0xc9, 0xf7, 0x4c, 0xde, 0xba, 0x15, 0xc9, 0xd5, 0xda, 0x06, 0xfa, 0x4d, 0x43, 0x26, 0x60, 0xe9,
	0xc5, 0xcd, 0x51, 0x7a, 0xd6, 0xb8, 0x33, 0x71, 0x8f, 0x87, 0xeb, 0xbb, 0x0d, 0xab, 0xcf, 0x6c,
	0x07, 0xfa, 0x91, 0x48, 0xd3, 0xb9, 0xf2, 0x6c, 0x73, 0x41, 0xa9, 0xd8, 0x01, 0xd8, 0xb2, 0x74,
	0xc1, 0x73, 0x8c, 0x3d, 0x5b, 0xff, 0xd8, 0x13, 0xd6, 0x25, 0xfa, 0x9a, 0x02, 0x3f, 0x63, 0xa4,
	0x3c, 0x18, 0x93, 0x89, 0x1d, 0x96, 0xca, 0x7f, 0x07, 0xce, 0x39, 0x2f, 0xe2, 0xe5, 0xf2, 0xaa,
	0xd1, 0x48, 0x63, 0x34, 0x06, 0xdd, 0x7b, 0xa1, 0xb0, 0x7a, 0x55, 0x3a, 0x6e, 0x30, 0x75, 0x9a,
	0x4c, 0xfe, 0x73, 0x70, 0xce, 0x9a, 0x2f, 0x21, 0x13, 0x31, 0x4a, 0x8f, 0x8c, 0x3b, 0x7a, 0x70,
	0x23, 0xfc, 0x05, 0x80, 0x2e, 0x39, 0xbb, 0xe1, 0x59, 0x82, 0xec, 0x35, 0xb8, 0x91, 0x89, 0x9a,
	0x3b, 0xda, 0x5d, 0x7b, 0x61, 0xcb, 0x4a, 0xb3, 0x26, 0x88, 0xea, 0x98, 0xed, 0x82, 0xa5, 0x2f,
	0x9c, 0xce, 0xe3, 0x92, 0xac, 0xaf, 0xe5, 0xdb, 0x98, 0x79, 0x60, 0x45, 0x22, 0x53, 0xf8, 0xb0,
	0x84, 0x1b, 0x84, 0x95, 0xdc, 0x3f, 0x02, 0xa7, 0xfe, 0xdd, 0xb0, 0x4d, 0x70, 0x8d, 0xb8, 0x10,
	0x45, 0xca, 0x6f, 0x69, 0x8b, 0xfd, 0x07, 0x9b, 0x26, 0xb1, 0xea, 0x49, 0xc9, 0xfe, 0x1f, 0x02,
	0x6e, 0xe3, 0xa1, 0x30, 0x80, 0x7e, 0x20, 0x93, 0xf3, 0xbb, 0x9c, 0xb6, 0x98, 0x0b, 0x56, 0x20,
	0x93, 0x53, 0xe4, 0x8a, 0x12, 0x36, 0x04, 0x08, 0x64, 0xf2, 0xa1, 0x10, 0xb9, 0x90, 0x48, 0xdb,
	0x6c, 0x03, 0x9c, 0x40, 0x26, 0x27, 0x79, 0x8e, 0x59, 0x4c, 0x3b, 0xec, 0x7f, 0xd8, 0xaa, 0x65,
	0x88, 0x32, 0x17, 0x99, 0x44, 0xda, 0x65, 0x0c, 0x86, 0x81, 0x4c, 0x42, 0xfc, 0x72, 0x87, 0x52,
	0x7d, 0x12, 0x0a, 0x69, 0x8f, 0x3d, 0x83, 0x9d, 0xf5, 0x5c, 0x5d, 0xdf, 0xd7, 0xd0, 0x81, 0x4c,
	0xaa, 0xed, 0x52, 0x8b, 0x51, 0x18, 0x68, 0x1e, 0xe4, 0x85, 0xba, 0xd6, 0x20, 0x36, 0xf3, 0x60,
	0xbb, 0x99, 0xa9, 0x0f, 0x3b, 0x25, 0xc3, 0x55, 0xc1, 0x33, 0x39, 0xc3, 0xe2, 0x3d, 0xf2, 0x18,
	0x0b, 0xea, 0xb2, 0x2d, 0xd8, 0xd0, 0xe9, 0x79, 0x8a, 0xe2, 0x4e, 0x5d, 0x88, 0xaf, 0x74, 0xb0,
	0x7f, 0x00, 0xc3, 0x75, 0xe7, 0xf5, 0xac, 0x27, 0x71, 0x7c, 0x21, 0x62, 0xa4, 0x2d, 0x3d, 0x6b,
	0x88, 0xa9, 0xb8, 0x47, 0xa3, 0xc9, 0x29, 0xfd, 0xf1, 0x38, 0x22, 0x3f, 0x1f, 0x47, 0xe4, 0xd7,
	0xe3, 0x88, 0x7c, 0xfb, 0x3d, 0x6a, 0x5d, 0xf7, 0xcd, 0xbf, 0xda, 0xcb, 0xbf, 0x01, 0x00, 0x00,
	0xff, 0xff, 0x2c, 0x70, 0xc6, 0xf6, 0xe6, 0x04, 0x00, 0x00,
}
