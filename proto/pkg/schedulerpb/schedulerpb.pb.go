// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: schedulerpb.proto

package schedulerpb

import (
	"fmt"
	"io"
	"math"

	proto "github.com/golang/protobuf/proto"

	_ "github.com/gogo/protobuf/gogoproto"

	eraftpb "github.com/pingcap-incubator/tinykv/proto/pkg/eraftpb"

	metapb "github.com/pingcap-incubator/tinykv/proto/pkg/metapb"

	context "golang.org/x/net/context"

	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ErrorType int32

const (
	ErrorType_OK                   ErrorType = 0
	ErrorType_UNKNOWN              ErrorType = 1
	ErrorType_NOT_BOOTSTRAPPED     ErrorType = 2
	ErrorType_STORE_TOMBSTONE      ErrorType = 3
	ErrorType_ALREADY_BOOTSTRAPPED ErrorType = 4
	ErrorType_INCOMPATIBLE_VERSION ErrorType = 5
	ErrorType_REGION_NOT_FOUND     ErrorType = 6
)

var ErrorType_name = map[int32]string{
	0: "OK",
	1: "UNKNOWN",
	2: "NOT_BOOTSTRAPPED",
	3: "STORE_TOMBSTONE",
	4: "ALREADY_BOOTSTRAPPED",
	5: "INCOMPATIBLE_VERSION",
	6: "REGION_NOT_FOUND",
}
var ErrorType_value = map[string]int32{
	"OK":                   0,
	"UNKNOWN":              1,
	"NOT_BOOTSTRAPPED":     2,
	"STORE_TOMBSTONE":      3,
	"ALREADY_BOOTSTRAPPED": 4,
	"INCOMPATIBLE_VERSION": 5,
	"REGION_NOT_FOUND":     6,
}

func (x ErrorType) String() string {
	return proto.EnumName(ErrorType_name, int32(x))
}
func (ErrorType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{0}
}

type OperatorStatus int32

const (
	OperatorStatus_SUCCESS OperatorStatus = 0
	OperatorStatus_TIMEOUT OperatorStatus = 1
	OperatorStatus_CANCEL  OperatorStatus = 2
	OperatorStatus_REPLACE OperatorStatus = 3
	OperatorStatus_RUNNING OperatorStatus = 4
)

var OperatorStatus_name = map[int32]string{
	0: "SUCCESS",
	1: "TIMEOUT",
	2: "CANCEL",
	3: "REPLACE",
	4: "RUNNING",
}
var OperatorStatus_value = map[string]int32{
	"SUCCESS": 0,
	"TIMEOUT": 1,
	"CANCEL":  2,
	"REPLACE": 3,
	"RUNNING": 4,
}

func (x OperatorStatus) String() string {
	return proto.EnumName(OperatorStatus_name, int32(x))
}
func (OperatorStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{1}
}

type RequestHeader struct {
	// cluster_id is the ID of the cluster which be sent to.
	ClusterId            uint64   `protobuf:"varint,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RequestHeader) Reset()         { *m = RequestHeader{} }
func (m *RequestHeader) String() string { return proto.CompactTextString(m) }
func (*RequestHeader) ProtoMessage()    {}
func (*RequestHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{0}
}
func (m *RequestHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RequestHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RequestHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RequestHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RequestHeader.Merge(dst, src)
}
func (m *RequestHeader) XXX_Size() int {
	return m.Size()
}
func (m *RequestHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_RequestHeader.DiscardUnknown(m)
}

var xxx_messageInfo_RequestHeader proto.InternalMessageInfo

func (m *RequestHeader) GetClusterId() uint64 {
	if m != nil {
		return m.ClusterId
	}
	return 0
}

type ResponseHeader struct {
	// cluster_id is the ID of the cluster which sent the response.
	ClusterId            uint64   `protobuf:"varint,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	Error                *Error   `protobuf:"bytes,2,opt,name=error" json:"error,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResponseHeader) Reset()         { *m = ResponseHeader{} }
func (m *ResponseHeader) String() string { return proto.CompactTextString(m) }
func (*ResponseHeader) ProtoMessage()    {}
func (*ResponseHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{1}
}
func (m *ResponseHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResponseHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResponseHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ResponseHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResponseHeader.Merge(dst, src)
}
func (m *ResponseHeader) XXX_Size() int {
	return m.Size()
}
func (m *ResponseHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_ResponseHeader.DiscardUnknown(m)
}

var xxx_messageInfo_ResponseHeader proto.InternalMessageInfo

func (m *ResponseHeader) GetClusterId() uint64 {
	if m != nil {
		return m.ClusterId
	}
	return 0
}

func (m *ResponseHeader) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

type Error struct {
	Type                 ErrorType `protobuf:"varint,1,opt,name=type,proto3,enum=schedulerpb.ErrorType" json:"type,omitempty"`
	Message              string    `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{2}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(dst, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetType() ErrorType {
	if m != nil {
		return m.Type
	}
	return ErrorType_OK
}

func (m *Error) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type TsoRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Count                uint32         `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *TsoRequest) Reset()         { *m = TsoRequest{} }
func (m *TsoRequest) String() string { return proto.CompactTextString(m) }
func (*TsoRequest) ProtoMessage()    {}
func (*TsoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{3}
}
func (m *TsoRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TsoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TsoRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *TsoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TsoRequest.Merge(dst, src)
}
func (m *TsoRequest) XXX_Size() int {
	return m.Size()
}
func (m *TsoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TsoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TsoRequest proto.InternalMessageInfo

func (m *TsoRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *TsoRequest) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type Timestamp struct {
	Physical             int64    `protobuf:"varint,1,opt,name=physical,proto3" json:"physical,omitempty"`
	Logical              int64    `protobuf:"varint,2,opt,name=logical,proto3" json:"logical,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Timestamp) Reset()         { *m = Timestamp{} }
func (m *Timestamp) String() string { return proto.CompactTextString(m) }
func (*Timestamp) ProtoMessage()    {}
func (*Timestamp) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{4}
}
func (m *Timestamp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Timestamp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Timestamp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *Timestamp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Timestamp.Merge(dst, src)
}
func (m *Timestamp) XXX_Size() int {
	return m.Size()
}
func (m *Timestamp) XXX_DiscardUnknown() {
	xxx_messageInfo_Timestamp.DiscardUnknown(m)
}

var xxx_messageInfo_Timestamp proto.InternalMessageInfo

func (m *Timestamp) GetPhysical() int64 {
	if m != nil {
		return m.Physical
	}
	return 0
}

func (m *Timestamp) GetLogical() int64 {
	if m != nil {
		return m.Logical
	}
	return 0
}

type TsoResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Count                uint32          `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Timestamp            *Timestamp      `protobuf:"bytes,3,opt,name=timestamp" json:"timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *TsoResponse) Reset()         { *m = TsoResponse{} }
func (m *TsoResponse) String() string { return proto.CompactTextString(m) }
func (*TsoResponse) ProtoMessage()    {}
func (*TsoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{5}
}
func (m *TsoResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TsoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TsoResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *TsoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TsoResponse.Merge(dst, src)
}
func (m *TsoResponse) XXX_Size() int {
	return m.Size()
}
func (m *TsoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TsoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TsoResponse proto.InternalMessageInfo

func (m *TsoResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *TsoResponse) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *TsoResponse) GetTimestamp() *Timestamp {
	if m != nil {
		return m.Timestamp
	}
	return nil
}

type BootstrapRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Store                *metapb.Store  `protobuf:"bytes,2,opt,name=store" json:"store,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BootstrapRequest) Reset()         { *m = BootstrapRequest{} }
func (m *BootstrapRequest) String() string { return proto.CompactTextString(m) }
func (*BootstrapRequest) ProtoMessage()    {}
func (*BootstrapRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{6}
}
func (m *BootstrapRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BootstrapRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BootstrapRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *BootstrapRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BootstrapRequest.Merge(dst, src)
}
func (m *BootstrapRequest) XXX_Size() int {
	return m.Size()
}
func (m *BootstrapRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BootstrapRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BootstrapRequest proto.InternalMessageInfo

func (m *BootstrapRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *BootstrapRequest) GetStore() *metapb.Store {
	if m != nil {
		return m.Store
	}
	return nil
}

type BootstrapResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BootstrapResponse) Reset()         { *m = BootstrapResponse{} }
func (m *BootstrapResponse) String() string { return proto.CompactTextString(m) }
func (*BootstrapResponse) ProtoMessage()    {}
func (*BootstrapResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{7}
}
func (m *BootstrapResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BootstrapResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BootstrapResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *BootstrapResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BootstrapResponse.Merge(dst, src)
}
func (m *BootstrapResponse) XXX_Size() int {
	return m.Size()
}
func (m *BootstrapResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BootstrapResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BootstrapResponse proto.InternalMessageInfo

func (m *BootstrapResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type IsBootstrappedRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *IsBootstrappedRequest) Reset()         { *m = IsBootstrappedRequest{} }
func (m *IsBootstrappedRequest) String() string { return proto.CompactTextString(m) }
func (*IsBootstrappedRequest) ProtoMessage()    {}
func (*IsBootstrappedRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{8}
}
func (m *IsBootstrappedRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IsBootstrappedRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IsBootstrappedRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *IsBootstrappedRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsBootstrappedRequest.Merge(dst, src)
}
func (m *IsBootstrappedRequest) XXX_Size() int {
	return m.Size()
}
func (m *IsBootstrappedRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IsBootstrappedRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IsBootstrappedRequest proto.InternalMessageInfo

func (m *IsBootstrappedRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type IsBootstrappedResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Bootstrapped         bool            `protobuf:"varint,2,opt,name=bootstrapped,proto3" json:"bootstrapped,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *IsBootstrappedResponse) Reset()         { *m = IsBootstrappedResponse{} }
func (m *IsBootstrappedResponse) String() string { return proto.CompactTextString(m) }
func (*IsBootstrappedResponse) ProtoMessage()    {}
func (*IsBootstrappedResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{9}
}
func (m *IsBootstrappedResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IsBootstrappedResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IsBootstrappedResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *IsBootstrappedResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsBootstrappedResponse.Merge(dst, src)
}
func (m *IsBootstrappedResponse) XXX_Size() int {
	return m.Size()
}
func (m *IsBootstrappedResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IsBootstrappedResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IsBootstrappedResponse proto.InternalMessageInfo

func (m *IsBootstrappedResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *IsBootstrappedResponse) GetBootstrapped() bool {
	if m != nil {
		return m.Bootstrapped
	}
	return false
}

type AllocIDRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AllocIDRequest) Reset()         { *m = AllocIDRequest{} }
func (m *AllocIDRequest) String() string { return proto.CompactTextString(m) }
func (*AllocIDRequest) ProtoMessage()    {}
func (*AllocIDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{10}
}
func (m *AllocIDRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AllocIDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AllocIDRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *AllocIDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllocIDRequest.Merge(dst, src)
}
func (m *AllocIDRequest) XXX_Size() int {
	return m.Size()
}
func (m *AllocIDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AllocIDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AllocIDRequest proto.InternalMessageInfo

func (m *AllocIDRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type AllocIDResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Id                   uint64          `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AllocIDResponse) Reset()         { *m = AllocIDResponse{} }
func (m *AllocIDResponse) String() string { return proto.CompactTextString(m) }
func (*AllocIDResponse) ProtoMessage()    {}
func (*AllocIDResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{11}
}
func (m *AllocIDResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AllocIDResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AllocIDResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *AllocIDResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllocIDResponse.Merge(dst, src)
}
func (m *AllocIDResponse) XXX_Size() int {
	return m.Size()
}
func (m *AllocIDResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AllocIDResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AllocIDResponse proto.InternalMessageInfo

func (m *AllocIDResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *AllocIDResponse) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetStoreRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	StoreId              uint64         `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetStoreRequest) Reset()         { *m = GetStoreRequest{} }
func (m *GetStoreRequest) String() string { return proto.CompactTextString(m) }
func (*GetStoreRequest) ProtoMessage()    {}
func (*GetStoreRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{12}
}
func (m *GetStoreRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetStoreRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetStoreRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetStoreRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoreRequest.Merge(dst, src)
}
func (m *GetStoreRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetStoreRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoreRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoreRequest proto.InternalMessageInfo

func (m *GetStoreRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetStoreRequest) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

type GetStoreResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Store                *metapb.Store   `protobuf:"bytes,2,opt,name=store" json:"store,omitempty"`
	Stats                *StoreStats     `protobuf:"bytes,3,opt,name=stats" json:"stats,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetStoreResponse) Reset()         { *m = GetStoreResponse{} }
func (m *GetStoreResponse) String() string { return proto.CompactTextString(m) }
func (*GetStoreResponse) ProtoMessage()    {}
func (*GetStoreResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{13}
}
func (m *GetStoreResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetStoreResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetStoreResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetStoreResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoreResponse.Merge(dst, src)
}
func (m *GetStoreResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetStoreResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoreResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoreResponse proto.InternalMessageInfo

func (m *GetStoreResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetStoreResponse) GetStore() *metapb.Store {
	if m != nil {
		return m.Store
	}
	return nil
}

func (m *GetStoreResponse) GetStats() *StoreStats {
	if m != nil {
		return m.Stats
	}
	return nil
}

type PutStoreRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Store                *metapb.Store  `protobuf:"bytes,2,opt,name=store" json:"store,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PutStoreRequest) Reset()         { *m = PutStoreRequest{} }
func (m *PutStoreRequest) String() string { return proto.CompactTextString(m) }
func (*PutStoreRequest) ProtoMessage()    {}
func (*PutStoreRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{14}
}
func (m *PutStoreRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutStoreRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutStoreRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *PutStoreRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutStoreRequest.Merge(dst, src)
}
func (m *PutStoreRequest) XXX_Size() int {
	return m.Size()
}
func (m *PutStoreRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PutStoreRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PutStoreRequest proto.InternalMessageInfo

func (m *PutStoreRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *PutStoreRequest) GetStore() *metapb.Store {
	if m != nil {
		return m.Store
	}
	return nil
}

type PutStoreResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PutStoreResponse) Reset()         { *m = PutStoreResponse{} }
func (m *PutStoreResponse) String() string { return proto.CompactTextString(m) }
func (*PutStoreResponse) ProtoMessage()    {}
func (*PutStoreResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{15}
}
func (m *PutStoreResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutStoreResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutStoreResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *PutStoreResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutStoreResponse.Merge(dst, src)
}
func (m *PutStoreResponse) XXX_Size() int {
	return m.Size()
}
func (m *PutStoreResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PutStoreResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PutStoreResponse proto.InternalMessageInfo

func (m *PutStoreResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type GetAllStoresRequest struct {
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	// Do NOT return tombstone stores if set to true.
	ExcludeTombstoneStores bool     `protobuf:"varint,2,opt,name=exclude_tombstone_stores,json=excludeTombstoneStores,proto3" json:"exclude_tombstone_stores,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GetAllStoresRequest) Reset()         { *m = GetAllStoresRequest{} }
func (m *GetAllStoresRequest) String() string { return proto.CompactTextString(m) }
func (*GetAllStoresRequest) ProtoMessage()    {}
func (*GetAllStoresRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{16}
}
func (m *GetAllStoresRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAllStoresRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAllStoresRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetAllStoresRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllStoresRequest.Merge(dst, src)
}
func (m *GetAllStoresRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetAllStoresRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllStoresRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllStoresRequest proto.InternalMessageInfo

func (m *GetAllStoresRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetAllStoresRequest) GetExcludeTombstoneStores() bool {
	if m != nil {
		return m.ExcludeTombstoneStores
	}
	return false
}

type GetAllStoresResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Stores               []*metapb.Store `protobuf:"bytes,2,rep,name=stores" json:"stores,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllStoresResponse) Reset()         { *m = GetAllStoresResponse{} }
func (m *GetAllStoresResponse) String() string { return proto.CompactTextString(m) }
func (*GetAllStoresResponse) ProtoMessage()    {}
func (*GetAllStoresResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{17}
}
func (m *GetAllStoresResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAllStoresResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAllStoresResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetAllStoresResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllStoresResponse.Merge(dst, src)
}
func (m *GetAllStoresResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetAllStoresResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllStoresResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllStoresResponse proto.InternalMessageInfo

func (m *GetAllStoresResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetAllStoresResponse) GetStores() []*metapb.Store {
	if m != nil {
		return m.Stores
	}
	return nil
}

type GetRegionRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	RegionKey            []byte         `protobuf:"bytes,2,opt,name=region_key,json=regionKey,proto3" json:"region_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetRegionRequest) Reset()         { *m = GetRegionRequest{} }
func (m *GetRegionRequest) String() string { return proto.CompactTextString(m) }
func (*GetRegionRequest) ProtoMessage()    {}
func (*GetRegionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{18}
}
func (m *GetRegionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRegionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRegionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetRegionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegionRequest.Merge(dst, src)
}
func (m *GetRegionRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetRegionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegionRequest proto.InternalMessageInfo

func (m *GetRegionRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetRegionRequest) GetRegionKey() []byte {
	if m != nil {
		return m.RegionKey
	}
	return nil
}

type GetRegionResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Region               *metapb.Region  `protobuf:"bytes,2,opt,name=region" json:"region,omitempty"`
	Leader               *metapb.Peer    `protobuf:"bytes,3,opt,name=leader" json:"leader,omitempty"`
	Slaves               []*metapb.Peer  `protobuf:"bytes,4,rep,name=slaves" json:"slaves,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetRegionResponse) Reset()         { *m = GetRegionResponse{} }
func (m *GetRegionResponse) String() string { return proto.CompactTextString(m) }
func (*GetRegionResponse) ProtoMessage()    {}
func (*GetRegionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{19}
}
func (m *GetRegionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRegionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRegionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetRegionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegionResponse.Merge(dst, src)
}
func (m *GetRegionResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetRegionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegionResponse proto.InternalMessageInfo

func (m *GetRegionResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetRegionResponse) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *GetRegionResponse) GetLeader() *metapb.Peer {
	if m != nil {
		return m.Leader
	}
	return nil
}

func (m *GetRegionResponse) GetSlaves() []*metapb.Peer {
	if m != nil {
		return m.Slaves
	}
	return nil
}

type GetRegionByIDRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	RegionId             uint64         `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetRegionByIDRequest) Reset()         { *m = GetRegionByIDRequest{} }
func (m *GetRegionByIDRequest) String() string { return proto.CompactTextString(m) }
func (*GetRegionByIDRequest) ProtoMessage()    {}
func (*GetRegionByIDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{20}
}
func (m *GetRegionByIDRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRegionByIDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRegionByIDRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetRegionByIDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegionByIDRequest.Merge(dst, src)
}
func (m *GetRegionByIDRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetRegionByIDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegionByIDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegionByIDRequest proto.InternalMessageInfo

func (m *GetRegionByIDRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetRegionByIDRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type ScanRegionsRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	StartKey             []byte         `protobuf:"bytes,2,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	Limit                int32          `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	EndKey               []byte         `protobuf:"bytes,4,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ScanRegionsRequest) Reset()         { *m = ScanRegionsRequest{} }
func (m *ScanRegionsRequest) String() string { return proto.CompactTextString(m) }
func (*ScanRegionsRequest) ProtoMessage()    {}
func (*ScanRegionsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{21}
}
func (m *ScanRegionsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ScanRegionsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ScanRegionsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ScanRegionsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanRegionsRequest.Merge(dst, src)
}
func (m *ScanRegionsRequest) XXX_Size() int {
	return m.Size()
}
func (m *ScanRegionsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanRegionsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ScanRegionsRequest proto.InternalMessageInfo

func (m *ScanRegionsRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *ScanRegionsRequest) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *ScanRegionsRequest) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ScanRegionsRequest) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

type ScanRegionsResponse struct {
	Header               *ResponseHeader  `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Regions              []*metapb.Region `protobuf:"bytes,2,rep,name=regions" json:"regions,omitempty"`
	Leaders              []*metapb.Peer   `protobuf:"bytes,3,rep,name=leaders" json:"leaders,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ScanRegionsResponse) Reset()         { *m = ScanRegionsResponse{} }
func (m *ScanRegionsResponse) String() string { return proto.CompactTextString(m) }
func (*ScanRegionsResponse) ProtoMessage()    {}
func (*ScanRegionsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{22}
}
func (m *ScanRegionsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ScanRegionsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ScanRegionsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ScanRegionsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanRegionsResponse.Merge(dst, src)
}
func (m *ScanRegionsResponse) XXX_Size() int {
	return m.Size()
}
func (m *ScanRegionsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanRegionsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ScanRegionsResponse proto.InternalMessageInfo

func (m *ScanRegionsResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *ScanRegionsResponse) GetRegions() []*metapb.Region {
	if m != nil {
		return m.Regions
	}
	return nil
}

func (m *ScanRegionsResponse) GetLeaders() []*metapb.Peer {
	if m != nil {
		return m.Leaders
	}
	return nil
}

type GetClusterConfigRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetClusterConfigRequest) Reset()         { *m = GetClusterConfigRequest{} }
func (m *GetClusterConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetClusterConfigRequest) ProtoMessage()    {}
func (*GetClusterConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{23}
}
func (m *GetClusterConfigRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetClusterConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetClusterConfigRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetClusterConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClusterConfigRequest.Merge(dst, src)
}
func (m *GetClusterConfigRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetClusterConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClusterConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetClusterConfigRequest proto.InternalMessageInfo

func (m *GetClusterConfigRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type GetClusterConfigResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Cluster              *metapb.Cluster `protobuf:"bytes,2,opt,name=cluster" json:"cluster,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetClusterConfigResponse) Reset()         { *m = GetClusterConfigResponse{} }
func (m *GetClusterConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetClusterConfigResponse) ProtoMessage()    {}
func (*GetClusterConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{24}
}
func (m *GetClusterConfigResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetClusterConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetClusterConfigResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetClusterConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClusterConfigResponse.Merge(dst, src)
}
func (m *GetClusterConfigResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetClusterConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClusterConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetClusterConfigResponse proto.InternalMessageInfo

func (m *GetClusterConfigResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetClusterConfigResponse) GetCluster() *metapb.Cluster {
	if m != nil {
		return m.Cluster
	}
	return nil
}

type PutClusterConfigRequest struct {
	Header               *RequestHeader  `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Cluster              *metapb.Cluster `protobuf:"bytes,2,opt,name=cluster" json:"cluster,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PutClusterConfigRequest) Reset()         { *m = PutClusterConfigRequest{} }
func (m *PutClusterConfigRequest) String() string { return proto.CompactTextString(m) }
func (*PutClusterConfigRequest) ProtoMessage()    {}
func (*PutClusterConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{25}
}
func (m *PutClusterConfigRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutClusterConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutClusterConfigRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *PutClusterConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutClusterConfigRequest.Merge(dst, src)
}
func (m *PutClusterConfigRequest) XXX_Size() int {
	return m.Size()
}
func (m *PutClusterConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PutClusterConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PutClusterConfigRequest proto.InternalMessageInfo

func (m *PutClusterConfigRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *PutClusterConfigRequest) GetCluster() *metapb.Cluster {
	if m != nil {
		return m.Cluster
	}
	return nil
}

type PutClusterConfigResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *PutClusterConfigResponse) Reset()         { *m = PutClusterConfigResponse{} }
func (m *PutClusterConfigResponse) String() string { return proto.CompactTextString(m) }
func (*PutClusterConfigResponse) ProtoMessage()    {}
func (*PutClusterConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{26}
}
func (m *PutClusterConfigResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutClusterConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutClusterConfigResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *PutClusterConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutClusterConfigResponse.Merge(dst, src)
}
func (m *PutClusterConfigResponse) XXX_Size() int {
	return m.Size()
}
func (m *PutClusterConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PutClusterConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PutClusterConfigResponse proto.InternalMessageInfo

func (m *PutClusterConfigResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type Member struct {
	// name is the name of the Scheduler member.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// member_id is the unique id of the Scheduler member.
	MemberId             uint64   `protobuf:"varint,2,opt,name=member_id,json=memberId,proto3" json:"member_id,omitempty"`
	PeerUrls             []string `protobuf:"bytes,3,rep,name=peer_urls,json=peerUrls" json:"peer_urls,omitempty"`
	ClientUrls           []string `protobuf:"bytes,4,rep,name=client_urls,json=clientUrls" json:"client_urls,omitempty"`
	LeaderPriority       int32    `protobuf:"varint,5,opt,name=leader_priority,json=leaderPriority,proto3" json:"leader_priority,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Member) Reset()         { *m = Member{} }
func (m *Member) String() string { return proto.CompactTextString(m) }
func (*Member) ProtoMessage()    {}
func (*Member) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{27}
}
func (m *Member) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Member) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Member.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *Member) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Member.Merge(dst, src)
}
func (m *Member) XXX_Size() int {
	return m.Size()
}
func (m *Member) XXX_DiscardUnknown() {
	xxx_messageInfo_Member.DiscardUnknown(m)
}

var xxx_messageInfo_Member proto.InternalMessageInfo

func (m *Member) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Member) GetMemberId() uint64 {
	if m != nil {
		return m.MemberId
	}
	return 0
}

func (m *Member) GetPeerUrls() []string {
	if m != nil {
		return m.PeerUrls
	}
	return nil
}

func (m *Member) GetClientUrls() []string {
	if m != nil {
		return m.ClientUrls
	}
	return nil
}

func (m *Member) GetLeaderPriority() int32 {
	if m != nil {
		return m.LeaderPriority
	}
	return 0
}

type GetMembersRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetMembersRequest) Reset()         { *m = GetMembersRequest{} }
func (m *GetMembersRequest) String() string { return proto.CompactTextString(m) }
func (*GetMembersRequest) ProtoMessage()    {}
func (*GetMembersRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{28}
}
func (m *GetMembersRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetMembersRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetMembersRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetMembersRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMembersRequest.Merge(dst, src)
}
func (m *GetMembersRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetMembersRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMembersRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMembersRequest proto.InternalMessageInfo

func (m *GetMembersRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type GetMembersResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Members              []*Member       `protobuf:"bytes,2,rep,name=members" json:"members,omitempty"`
	Leader               *Member         `protobuf:"bytes,3,opt,name=leader" json:"leader,omitempty"`
	EtcdLeader           *Member         `protobuf:"bytes,4,opt,name=etcd_leader,json=etcdLeader" json:"etcd_leader,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetMembersResponse) Reset()         { *m = GetMembersResponse{} }
func (m *GetMembersResponse) String() string { return proto.CompactTextString(m) }
func (*GetMembersResponse) ProtoMessage()    {}
func (*GetMembersResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{29}
}
func (m *GetMembersResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetMembersResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetMembersResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetMembersResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMembersResponse.Merge(dst, src)
}
func (m *GetMembersResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetMembersResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMembersResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMembersResponse proto.InternalMessageInfo

func (m *GetMembersResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetMembersResponse) GetMembers() []*Member {
	if m != nil {
		return m.Members
	}
	return nil
}

func (m *GetMembersResponse) GetLeader() *Member {
	if m != nil {
		return m.Leader
	}
	return nil
}

func (m *GetMembersResponse) GetEtcdLeader() *Member {
	if m != nil {
		return m.EtcdLeader
	}
	return nil
}

type RegionHeartbeatRequest struct {
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Region *metapb.Region `protobuf:"bytes,2,opt,name=region" json:"region,omitempty"`
	// Leader Peer sending the heartbeat.
	Leader *metapb.Peer `protobuf:"bytes,3,opt,name=leader" json:"leader,omitempty"`
	// Pending peers are the peers that the leader can't consider as
	// working followers.
	PendingPeers []*metapb.Peer `protobuf:"bytes,5,rep,name=pending_peers,json=pendingPeers" json:"pending_peers,omitempty"`
	// Approximate region size.
	ApproximateSize      uint64   `protobuf:"varint,10,opt,name=approximate_size,json=approximateSize,proto3" json:"approximate_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegionHeartbeatRequest) Reset()         { *m = RegionHeartbeatRequest{} }
func (m *RegionHeartbeatRequest) String() string { return proto.CompactTextString(m) }
func (*RegionHeartbeatRequest) ProtoMessage()    {}
func (*RegionHeartbeatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{30}
}
func (m *RegionHeartbeatRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionHeartbeatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionHeartbeatRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RegionHeartbeatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionHeartbeatRequest.Merge(dst, src)
}
func (m *RegionHeartbeatRequest) XXX_Size() int {
	return m.Size()
}
func (m *RegionHeartbeatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionHeartbeatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RegionHeartbeatRequest proto.InternalMessageInfo

func (m *RegionHeartbeatRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *RegionHeartbeatRequest) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *RegionHeartbeatRequest) GetLeader() *metapb.Peer {
	if m != nil {
		return m.Leader
	}
	return nil
}

func (m *RegionHeartbeatRequest) GetPendingPeers() []*metapb.Peer {
	if m != nil {
		return m.PendingPeers
	}
	return nil
}

func (m *RegionHeartbeatRequest) GetApproximateSize() uint64 {
	if m != nil {
		return m.ApproximateSize
	}
	return 0
}

type ChangePeer struct {
	Peer                 *metapb.Peer           `protobuf:"bytes,1,opt,name=peer" json:"peer,omitempty"`
	ChangeType           eraftpb.ConfChangeType `protobuf:"varint,2,opt,name=change_type,json=changeType,proto3,enum=eraftpb.ConfChangeType" json:"change_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ChangePeer) Reset()         { *m = ChangePeer{} }
func (m *ChangePeer) String() string { return proto.CompactTextString(m) }
func (*ChangePeer) ProtoMessage()    {}
func (*ChangePeer) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{31}
}
func (m *ChangePeer) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangePeer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangePeer.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ChangePeer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangePeer.Merge(dst, src)
}
func (m *ChangePeer) XXX_Size() int {
	return m.Size()
}
func (m *ChangePeer) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangePeer.DiscardUnknown(m)
}

var xxx_messageInfo_ChangePeer proto.InternalMessageInfo

func (m *ChangePeer) GetPeer() *metapb.Peer {
	if m != nil {
		return m.Peer
	}
	return nil
}

func (m *ChangePeer) GetChangeType() eraftpb.ConfChangeType {
	if m != nil {
		return m.ChangeType
	}
	return eraftpb.ConfChangeType_AddNode
}

type TransferLeader struct {
	Peer                 *metapb.Peer `protobuf:"bytes,1,opt,name=peer" json:"peer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TransferLeader) Reset()         { *m = TransferLeader{} }
func (m *TransferLeader) String() string { return proto.CompactTextString(m) }
func (*TransferLeader) ProtoMessage()    {}
func (*TransferLeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{32}
}
func (m *TransferLeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TransferLeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TransferLeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *TransferLeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferLeader.Merge(dst, src)
}
func (m *TransferLeader) XXX_Size() int {
	return m.Size()
}
func (m *TransferLeader) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferLeader.DiscardUnknown(m)
}

var xxx_messageInfo_TransferLeader proto.InternalMessageInfo

func (m *TransferLeader) GetPeer() *metapb.Peer {
	if m != nil {
		return m.Peer
	}
	return nil
}

type RegionHeartbeatResponse struct {
	Header *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	// Notice, Scheduleeer only allows handling reported epoch >= current scheduler's.
	// Leader peer reports region status with RegionHeartbeatRequest
	// to scheduler regularly, scheduler will determine whether this region
	// should do ChangePeer or not.
	// E,g, max peer number is 3, region A, first only peer 1 in A.
	// 1. Scheduler region state -> Peers (1), ConfVer (1).
	// 2. Leader peer 1 reports region state to scheduler, scheduler finds the
	// peer number is < 3, so first changes its current region
	// state -> Peers (1, 2), ConfVer (1), and returns ChangePeer Adding 2.
	// 3. Leader does ChangePeer, then reports Peers (1, 2), ConfVer (2),
	// scheduler updates its state -> Peers (1, 2), ConfVer (2).
	// 4. Leader may report old Peers (1), ConfVer (1) to scheduler before ConfChange
	// finished, scheduler stills responses ChangePeer Adding 2, of course, we must
	// guarantee the second ChangePeer can't be applied in TiKV.
	ChangePeer *ChangePeer `protobuf:"bytes,2,opt,name=change_peer,json=changePeer" json:"change_peer,omitempty"`
	// Scheduler can return transfer_leader to let TiKV does leader transfer itself.
	TransferLeader *TransferLeader `protobuf:"bytes,3,opt,name=transfer_leader,json=transferLeader" json:"transfer_leader,omitempty"`
	// ID of the region
	RegionId    uint64              `protobuf:"varint,4,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	RegionEpoch *metapb.RegionEpoch `protobuf:"bytes,5,opt,name=region_epoch,json=regionEpoch" json:"region_epoch,omitempty"`
	// Leader of the region at the moment of the corresponding request was made.
	TargetPeer           *metapb.Peer `protobuf:"bytes,6,opt,name=target_peer,json=targetPeer" json:"target_peer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RegionHeartbeatResponse) Reset()         { *m = RegionHeartbeatResponse{} }
func (m *RegionHeartbeatResponse) String() string { return proto.CompactTextString(m) }
func (*RegionHeartbeatResponse) ProtoMessage()    {}
func (*RegionHeartbeatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{33}
}
func (m *RegionHeartbeatResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionHeartbeatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionHeartbeatResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RegionHeartbeatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionHeartbeatResponse.Merge(dst, src)
}
func (m *RegionHeartbeatResponse) XXX_Size() int {
	return m.Size()
}
func (m *RegionHeartbeatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionHeartbeatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RegionHeartbeatResponse proto.InternalMessageInfo

func (m *RegionHeartbeatResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetChangePeer() *ChangePeer {
	if m != nil {
		return m.ChangePeer
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetTransferLeader() *TransferLeader {
	if m != nil {
		return m.TransferLeader
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RegionHeartbeatResponse) GetRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetTargetPeer() *metapb.Peer {
	if m != nil {
		return m.TargetPeer
	}
	return nil
}

type AskSplitRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Region               *metapb.Region `protobuf:"bytes,2,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AskSplitRequest) Reset()         { *m = AskSplitRequest{} }
func (m *AskSplitRequest) String() string { return proto.CompactTextString(m) }
func (*AskSplitRequest) ProtoMessage()    {}
func (*AskSplitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{34}
}
func (m *AskSplitRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AskSplitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AskSplitRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *AskSplitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AskSplitRequest.Merge(dst, src)
}
func (m *AskSplitRequest) XXX_Size() int {
	return m.Size()
}
func (m *AskSplitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AskSplitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AskSplitRequest proto.InternalMessageInfo

func (m *AskSplitRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *AskSplitRequest) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

type AskSplitResponse struct {
	Header *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	// We split the region into two, first uses the origin
	// parent region id, and the second uses the new_region_id.
	// We must guarantee that the new_region_id is global unique.
	NewRegionId uint64 `protobuf:"varint,2,opt,name=new_region_id,json=newRegionId,proto3" json:"new_region_id,omitempty"`
	// The peer ids for the new split region.
	NewPeerIds           []uint64 `protobuf:"varint,3,rep,packed,name=new_peer_ids,json=newPeerIds" json:"new_peer_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AskSplitResponse) Reset()         { *m = AskSplitResponse{} }
func (m *AskSplitResponse) String() string { return proto.CompactTextString(m) }
func (*AskSplitResponse) ProtoMessage()    {}
func (*AskSplitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{35}
}
func (m *AskSplitResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AskSplitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AskSplitResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *AskSplitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AskSplitResponse.Merge(dst, src)
}
func (m *AskSplitResponse) XXX_Size() int {
	return m.Size()
}
func (m *AskSplitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AskSplitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AskSplitResponse proto.InternalMessageInfo

func (m *AskSplitResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *AskSplitResponse) GetNewRegionId() uint64 {
	if m != nil {
		return m.NewRegionId
	}
	return 0
}

func (m *AskSplitResponse) GetNewPeerIds() []uint64 {
	if m != nil {
		return m.NewPeerIds
	}
	return nil
}

type ReportSplitRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Left                 *metapb.Region `protobuf:"bytes,2,opt,name=left" json:"left,omitempty"`
	Right                *metapb.Region `protobuf:"bytes,3,opt,name=right" json:"right,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ReportSplitRequest) Reset()         { *m = ReportSplitRequest{} }
func (m *ReportSplitRequest) String() string { return proto.CompactTextString(m) }
func (*ReportSplitRequest) ProtoMessage()    {}
func (*ReportSplitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{36}
}
func (m *ReportSplitRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReportSplitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReportSplitRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ReportSplitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportSplitRequest.Merge(dst, src)
}
func (m *ReportSplitRequest) XXX_Size() int {
	return m.Size()
}
func (m *ReportSplitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportSplitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportSplitRequest proto.InternalMessageInfo

func (m *ReportSplitRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *ReportSplitRequest) GetLeft() *metapb.Region {
	if m != nil {
		return m.Left
	}
	return nil
}

func (m *ReportSplitRequest) GetRight() *metapb.Region {
	if m != nil {
		return m.Right
	}
	return nil
}

type ReportSplitResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ReportSplitResponse) Reset()         { *m = ReportSplitResponse{} }
func (m *ReportSplitResponse) String() string { return proto.CompactTextString(m) }
func (*ReportSplitResponse) ProtoMessage()    {}
func (*ReportSplitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{37}
}
func (m *ReportSplitResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReportSplitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReportSplitResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ReportSplitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportSplitResponse.Merge(dst, src)
}
func (m *ReportSplitResponse) XXX_Size() int {
	return m.Size()
}
func (m *ReportSplitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportSplitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportSplitResponse proto.InternalMessageInfo

func (m *ReportSplitResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type SplitID struct {
	NewRegionId          uint64   `protobuf:"varint,1,opt,name=new_region_id,json=newRegionId,proto3" json:"new_region_id,omitempty"`
	NewPeerIds           []uint64 `protobuf:"varint,2,rep,packed,name=new_peer_ids,json=newPeerIds" json:"new_peer_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SplitID) Reset()         { *m = SplitID{} }
func (m *SplitID) String() string { return proto.CompactTextString(m) }
func (*SplitID) ProtoMessage()    {}
func (*SplitID) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{38}
}
func (m *SplitID) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SplitID) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SplitID.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *SplitID) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SplitID.Merge(dst, src)
}
func (m *SplitID) XXX_Size() int {
	return m.Size()
}
func (m *SplitID) XXX_DiscardUnknown() {
	xxx_messageInfo_SplitID.DiscardUnknown(m)
}

var xxx_messageInfo_SplitID proto.InternalMessageInfo

func (m *SplitID) GetNewRegionId() uint64 {
	if m != nil {
		return m.NewRegionId
	}
	return 0
}

func (m *SplitID) GetNewPeerIds() []uint64 {
	if m != nil {
		return m.NewPeerIds
	}
	return nil
}

type TimeInterval struct {
	// The unix timestamp in seconds of the start of this period.
	StartTimestamp uint64 `protobuf:"varint,1,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	// The unix timestamp in seconds of the end of this period.
	EndTimestamp         uint64   `protobuf:"varint,2,opt,name=end_timestamp,json=endTimestamp,proto3" json:"end_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeInterval) Reset()         { *m = TimeInterval{} }
func (m *TimeInterval) String() string { return proto.CompactTextString(m) }
func (*TimeInterval) ProtoMessage()    {}
func (*TimeInterval) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{39}
}
func (m *TimeInterval) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TimeInterval) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TimeInterval.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *TimeInterval) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeInterval.Merge(dst, src)
}
func (m *TimeInterval) XXX_Size() int {
	return m.Size()
}
func (m *TimeInterval) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeInterval.DiscardUnknown(m)
}

var xxx_messageInfo_TimeInterval proto.InternalMessageInfo

func (m *TimeInterval) GetStartTimestamp() uint64 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *TimeInterval) GetEndTimestamp() uint64 {
	if m != nil {
		return m.EndTimestamp
	}
	return 0
}

type RecordPair struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value                uint64   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordPair) Reset()         { *m = RecordPair{} }
func (m *RecordPair) String() string { return proto.CompactTextString(m) }
func (*RecordPair) ProtoMessage()    {}
func (*RecordPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{40}
}
func (m *RecordPair) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RecordPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RecordPair.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RecordPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordPair.Merge(dst, src)
}
func (m *RecordPair) XXX_Size() int {
	return m.Size()
}
func (m *RecordPair) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordPair.DiscardUnknown(m)
}

var xxx_messageInfo_RecordPair proto.InternalMessageInfo

func (m *RecordPair) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *RecordPair) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

type StoreStats struct {
	StoreId uint64 `protobuf:"varint,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// Capacity for the store.
	Capacity uint64 `protobuf:"varint,2,opt,name=capacity,proto3" json:"capacity,omitempty"`
	// Available size for the store.
	Available uint64 `protobuf:"varint,3,opt,name=available,proto3" json:"available,omitempty"`
	// Total region count in this store.
	RegionCount uint32 `protobuf:"varint,4,opt,name=region_count,json=regionCount,proto3" json:"region_count,omitempty"`
	// Current sending snapshot count.
	SendingSnapCount uint32 `protobuf:"varint,5,opt,name=sending_snap_count,json=sendingSnapCount,proto3" json:"sending_snap_count,omitempty"`
	// Current receiving snapshot count.
	ReceivingSnapCount uint32 `protobuf:"varint,6,opt,name=receiving_snap_count,json=receivingSnapCount,proto3" json:"receiving_snap_count,omitempty"`
	// When the store is started (unix timestamp in seconds).
	StartTime uint32 `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// How many region is applying snapshot.
	ApplyingSnapCount uint32 `protobuf:"varint,8,opt,name=applying_snap_count,json=applyingSnapCount,proto3" json:"applying_snap_count,omitempty"`
	// If the store is busy
	IsBusy bool `protobuf:"varint,9,opt,name=is_busy,json=isBusy,proto3" json:"is_busy,omitempty"`
	// Actually used space by db
	UsedSize uint64 `protobuf:"varint,10,opt,name=used_size,json=usedSize,proto3" json:"used_size,omitempty"`
	// Actually reported time interval
	Interval *TimeInterval `protobuf:"bytes,15,opt,name=interval" json:"interval,omitempty"`
	// Threads' CPU usages in the store
	CpuUsages []*RecordPair `protobuf:"bytes,16,rep,name=cpu_usages,json=cpuUsages" json:"cpu_usages,omitempty"`
	// Threads' read disk I/O rates in the store
	ReadIoRates []*RecordPair `protobuf:"bytes,17,rep,name=read_io_rates,json=readIoRates" json:"read_io_rates,omitempty"`
	// Threads' write disk I/O rates in the store
	WriteIoRates []*RecordPair `protobuf:"bytes,18,rep,name=write_io_rates,json=writeIoRates" json:"write_io_rates,omitempty"`
	// Operations' latencies in the store
	OpLatencies          []*RecordPair `protobuf:"bytes,19,rep,name=op_latencies,json=opLatencies" json:"op_latencies,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StoreStats) Reset()         { *m = StoreStats{} }
func (m *StoreStats) String() string { return proto.CompactTextString(m) }
func (*StoreStats) ProtoMessage()    {}
func (*StoreStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{41}
}
func (m *StoreStats) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreStats.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *StoreStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreStats.Merge(dst, src)
}
func (m *StoreStats) XXX_Size() int {
	return m.Size()
}
func (m *StoreStats) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreStats.DiscardUnknown(m)
}

var xxx_messageInfo_StoreStats proto.InternalMessageInfo

func (m *StoreStats) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *StoreStats) GetCapacity() uint64 {
	if m != nil {
		return m.Capacity
	}
	return 0
}

func (m *StoreStats) GetAvailable() uint64 {
	if m != nil {
		return m.Available
	}
	return 0
}

func (m *StoreStats) GetRegionCount() uint32 {
	if m != nil {
		return m.RegionCount
	}
	return 0
}

func (m *StoreStats) GetSendingSnapCount() uint32 {
	if m != nil {
		return m.SendingSnapCount
	}
	return 0
}

func (m *StoreStats) GetReceivingSnapCount() uint32 {
	if m != nil {
		return m.ReceivingSnapCount
	}
	return 0
}

func (m *StoreStats) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *StoreStats) GetApplyingSnapCount() uint32 {
	if m != nil {
		return m.ApplyingSnapCount
	}
	return 0
}

func (m *StoreStats) GetIsBusy() bool {
	if m != nil {
		return m.IsBusy
	}
	return false
}

func (m *StoreStats) GetUsedSize() uint64 {
	if m != nil {
		return m.UsedSize
	}
	return 0
}

func (m *StoreStats) GetInterval() *TimeInterval {
	if m != nil {
		return m.Interval
	}
	return nil
}

func (m *StoreStats) GetCpuUsages() []*RecordPair {
	if m != nil {
		return m.CpuUsages
	}
	return nil
}

func (m *StoreStats) GetReadIoRates() []*RecordPair {
	if m != nil {
		return m.ReadIoRates
	}
	return nil
}

func (m *StoreStats) GetWriteIoRates() []*RecordPair {
	if m != nil {
		return m.WriteIoRates
	}
	return nil
}

func (m *StoreStats) GetOpLatencies() []*RecordPair {
	if m != nil {
		return m.OpLatencies
	}
	return nil
}

type StoreHeartbeatRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Stats                *StoreStats    `protobuf:"bytes,2,opt,name=stats" json:"stats,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *StoreHeartbeatRequest) Reset()         { *m = StoreHeartbeatRequest{} }
func (m *StoreHeartbeatRequest) String() string { return proto.CompactTextString(m) }
func (*StoreHeartbeatRequest) ProtoMessage()    {}
func (*StoreHeartbeatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{42}
}
func (m *StoreHeartbeatRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreHeartbeatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreHeartbeatRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *StoreHeartbeatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreHeartbeatRequest.Merge(dst, src)
}
func (m *StoreHeartbeatRequest) XXX_Size() int {
	return m.Size()
}
func (m *StoreHeartbeatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreHeartbeatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StoreHeartbeatRequest proto.InternalMessageInfo

func (m *StoreHeartbeatRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *StoreHeartbeatRequest) GetStats() *StoreStats {
	if m != nil {
		return m.Stats
	}
	return nil
}

type StoreHeartbeatResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *StoreHeartbeatResponse) Reset()         { *m = StoreHeartbeatResponse{} }
func (m *StoreHeartbeatResponse) String() string { return proto.CompactTextString(m) }
func (*StoreHeartbeatResponse) ProtoMessage()    {}
func (*StoreHeartbeatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{43}
}
func (m *StoreHeartbeatResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreHeartbeatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreHeartbeatResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *StoreHeartbeatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreHeartbeatResponse.Merge(dst, src)
}
func (m *StoreHeartbeatResponse) XXX_Size() int {
	return m.Size()
}
func (m *StoreHeartbeatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreHeartbeatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StoreHeartbeatResponse proto.InternalMessageInfo

func (m *StoreHeartbeatResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type ScatterRegionRequest struct {
	Header   *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	RegionId uint64         `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	// Scheduler will use these region information if it can't find the region.
	// For example, the region is just split and hasn't report to Scheduler yet.
	Region               *metapb.Region `protobuf:"bytes,3,opt,name=region" json:"region,omitempty"`
	Leader               *metapb.Peer   `protobuf:"bytes,4,opt,name=leader" json:"leader,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ScatterRegionRequest) Reset()         { *m = ScatterRegionRequest{} }
func (m *ScatterRegionRequest) String() string { return proto.CompactTextString(m) }
func (*ScatterRegionRequest) ProtoMessage()    {}
func (*ScatterRegionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{44}
}
func (m *ScatterRegionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ScatterRegionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ScatterRegionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ScatterRegionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScatterRegionRequest.Merge(dst, src)
}
func (m *ScatterRegionRequest) XXX_Size() int {
	return m.Size()
}
func (m *ScatterRegionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ScatterRegionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ScatterRegionRequest proto.InternalMessageInfo

func (m *ScatterRegionRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *ScatterRegionRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *ScatterRegionRequest) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *ScatterRegionRequest) GetLeader() *metapb.Peer {
	if m != nil {
		return m.Leader
	}
	return nil
}

type ScatterRegionResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ScatterRegionResponse) Reset()         { *m = ScatterRegionResponse{} }
func (m *ScatterRegionResponse) String() string { return proto.CompactTextString(m) }
func (*ScatterRegionResponse) ProtoMessage()    {}
func (*ScatterRegionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{45}
}
func (m *ScatterRegionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ScatterRegionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ScatterRegionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ScatterRegionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScatterRegionResponse.Merge(dst, src)
}
func (m *ScatterRegionResponse) XXX_Size() int {
	return m.Size()
}
func (m *ScatterRegionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ScatterRegionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ScatterRegionResponse proto.InternalMessageInfo

func (m *ScatterRegionResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type GetGCSafePointRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetGCSafePointRequest) Reset()         { *m = GetGCSafePointRequest{} }
func (m *GetGCSafePointRequest) String() string { return proto.CompactTextString(m) }
func (*GetGCSafePointRequest) ProtoMessage()    {}
func (*GetGCSafePointRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{46}
}
func (m *GetGCSafePointRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetGCSafePointRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetGCSafePointRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetGCSafePointRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGCSafePointRequest.Merge(dst, src)
}
func (m *GetGCSafePointRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetGCSafePointRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGCSafePointRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetGCSafePointRequest proto.InternalMessageInfo

func (m *GetGCSafePointRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type GetGCSafePointResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	SafePoint            uint64          `protobuf:"varint,2,opt,name=safe_point,json=safePoint,proto3" json:"safe_point,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGCSafePointResponse) Reset()         { *m = GetGCSafePointResponse{} }
func (m *GetGCSafePointResponse) String() string { return proto.CompactTextString(m) }
func (*GetGCSafePointResponse) ProtoMessage()    {}
func (*GetGCSafePointResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{47}
}
func (m *GetGCSafePointResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetGCSafePointResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetGCSafePointResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetGCSafePointResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGCSafePointResponse.Merge(dst, src)
}
func (m *GetGCSafePointResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetGCSafePointResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGCSafePointResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetGCSafePointResponse proto.InternalMessageInfo

func (m *GetGCSafePointResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetGCSafePointResponse) GetSafePoint() uint64 {
	if m != nil {
		return m.SafePoint
	}
	return 0
}

type UpdateGCSafePointRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	SafePoint            uint64         `protobuf:"varint,2,opt,name=safe_point,json=safePoint,proto3" json:"safe_point,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UpdateGCSafePointRequest) Reset()         { *m = UpdateGCSafePointRequest{} }
func (m *UpdateGCSafePointRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateGCSafePointRequest) ProtoMessage()    {}
func (*UpdateGCSafePointRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{48}
}
func (m *UpdateGCSafePointRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateGCSafePointRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateGCSafePointRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *UpdateGCSafePointRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGCSafePointRequest.Merge(dst, src)
}
func (m *UpdateGCSafePointRequest) XXX_Size() int {
	return m.Size()
}
func (m *UpdateGCSafePointRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGCSafePointRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGCSafePointRequest proto.InternalMessageInfo

func (m *UpdateGCSafePointRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *UpdateGCSafePointRequest) GetSafePoint() uint64 {
	if m != nil {
		return m.SafePoint
	}
	return 0
}

type UpdateGCSafePointResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	NewSafePoint         uint64          `protobuf:"varint,2,opt,name=new_safe_point,json=newSafePoint,proto3" json:"new_safe_point,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateGCSafePointResponse) Reset()         { *m = UpdateGCSafePointResponse{} }
func (m *UpdateGCSafePointResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateGCSafePointResponse) ProtoMessage()    {}
func (*UpdateGCSafePointResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{49}
}
func (m *UpdateGCSafePointResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateGCSafePointResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateGCSafePointResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *UpdateGCSafePointResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGCSafePointResponse.Merge(dst, src)
}
func (m *UpdateGCSafePointResponse) XXX_Size() int {
	return m.Size()
}
func (m *UpdateGCSafePointResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGCSafePointResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGCSafePointResponse proto.InternalMessageInfo

func (m *UpdateGCSafePointResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *UpdateGCSafePointResponse) GetNewSafePoint() uint64 {
	if m != nil {
		return m.NewSafePoint
	}
	return 0
}

type GetOperatorRequest struct {
	Header               *RequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	RegionId             uint64         `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetOperatorRequest) Reset()         { *m = GetOperatorRequest{} }
func (m *GetOperatorRequest) String() string { return proto.CompactTextString(m) }
func (*GetOperatorRequest) ProtoMessage()    {}
func (*GetOperatorRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{50}
}
func (m *GetOperatorRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetOperatorRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetOperatorRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetOperatorRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperatorRequest.Merge(dst, src)
}
func (m *GetOperatorRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetOperatorRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperatorRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperatorRequest proto.InternalMessageInfo

func (m *GetOperatorRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetOperatorRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type GetOperatorResponse struct {
	Header               *ResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	RegionId             uint64          `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	Desc                 []byte          `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Status               OperatorStatus  `protobuf:"varint,4,opt,name=status,proto3,enum=schedulerpb.OperatorStatus" json:"status,omitempty"`
	Kind                 []byte          `protobuf:"bytes,5,opt,name=kind,proto3" json:"kind,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetOperatorResponse) Reset()         { *m = GetOperatorResponse{} }
func (m *GetOperatorResponse) String() string { return proto.CompactTextString(m) }
func (*GetOperatorResponse) ProtoMessage()    {}
func (*GetOperatorResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_schedulerpb_4e333137f5959f12, []int{51}
}
func (m *GetOperatorResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetOperatorResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetOperatorResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetOperatorResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperatorResponse.Merge(dst, src)
}
func (m *GetOperatorResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetOperatorResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperatorResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperatorResponse proto.InternalMessageInfo

func (m *GetOperatorResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetOperatorResponse) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *GetOperatorResponse) GetDesc() []byte {
	if m != nil {
		return m.Desc
	}
	return nil
}

func (m *GetOperatorResponse) GetStatus() OperatorStatus {
	if m != nil {
		return m.Status
	}
	return OperatorStatus_SUCCESS
}

func (m *GetOperatorResponse) GetKind() []byte {
	if m != nil {
		return m.Kind
	}
	return nil
}

func init() {
	proto.RegisterType((*RequestHeader)(nil), "schedulerpb.RequestHeader")
	proto.RegisterType((*ResponseHeader)(nil), "schedulerpb.ResponseHeader")
	proto.RegisterType((*Error)(nil), "schedulerpb.Error")
	proto.RegisterType((*TsoRequest)(nil), "schedulerpb.TsoRequest")
	proto.RegisterType((*Timestamp)(nil), "schedulerpb.Timestamp")
	proto.RegisterType((*TsoResponse)(nil), "schedulerpb.TsoResponse")
	proto.RegisterType((*BootstrapRequest)(nil), "schedulerpb.BootstrapRequest")
	proto.RegisterType((*BootstrapResponse)(nil), "schedulerpb.BootstrapResponse")
	proto.RegisterType((*IsBootstrappedRequest)(nil), "schedulerpb.IsBootstrappedRequest")
	proto.RegisterType((*IsBootstrappedResponse)(nil), "schedulerpb.IsBootstrappedResponse")
	proto.RegisterType((*AllocIDRequest)(nil), "schedulerpb.AllocIDRequest")
	proto.RegisterType((*AllocIDResponse)(nil), "schedulerpb.AllocIDResponse")
	proto.RegisterType((*GetStoreRequest)(nil), "schedulerpb.GetStoreRequest")
	proto.RegisterType((*GetStoreResponse)(nil), "schedulerpb.GetStoreResponse")
	proto.RegisterType((*PutStoreRequest)(nil), "schedulerpb.PutStoreRequest")
	proto.RegisterType((*PutStoreResponse)(nil), "schedulerpb.PutStoreResponse")
	proto.RegisterType((*GetAllStoresRequest)(nil), "schedulerpb.GetAllStoresRequest")
	proto.RegisterType((*GetAllStoresResponse)(nil), "schedulerpb.GetAllStoresResponse")
	proto.RegisterType((*GetRegionRequest)(nil), "schedulerpb.GetRegionRequest")
	proto.RegisterType((*GetRegionResponse)(nil), "schedulerpb.GetRegionResponse")
	proto.RegisterType((*GetRegionByIDRequest)(nil), "schedulerpb.GetRegionByIDRequest")
	proto.RegisterType((*ScanRegionsRequest)(nil), "schedulerpb.ScanRegionsRequest")
	proto.RegisterType((*ScanRegionsResponse)(nil), "schedulerpb.ScanRegionsResponse")
	proto.RegisterType((*GetClusterConfigRequest)(nil), "schedulerpb.GetClusterConfigRequest")
	proto.RegisterType((*GetClusterConfigResponse)(nil), "schedulerpb.GetClusterConfigResponse")
	proto.RegisterType((*PutClusterConfigRequest)(nil), "schedulerpb.PutClusterConfigRequest")
	proto.RegisterType((*PutClusterConfigResponse)(nil), "schedulerpb.PutClusterConfigResponse")
	proto.RegisterType((*Member)(nil), "schedulerpb.Member")
	proto.RegisterType((*GetMembersRequest)(nil), "schedulerpb.GetMembersRequest")
	proto.RegisterType((*GetMembersResponse)(nil), "schedulerpb.GetMembersResponse")
	proto.RegisterType((*RegionHeartbeatRequest)(nil), "schedulerpb.RegionHeartbeatRequest")
	proto.RegisterType((*ChangePeer)(nil), "schedulerpb.ChangePeer")
	proto.RegisterType((*TransferLeader)(nil), "schedulerpb.TransferLeader")
	proto.RegisterType((*RegionHeartbeatResponse)(nil), "schedulerpb.RegionHeartbeatResponse")
	proto.RegisterType((*AskSplitRequest)(nil), "schedulerpb.AskSplitRequest")
	proto.RegisterType((*AskSplitResponse)(nil), "schedulerpb.AskSplitResponse")
	proto.RegisterType((*ReportSplitRequest)(nil), "schedulerpb.ReportSplitRequest")
	proto.RegisterType((*ReportSplitResponse)(nil), "schedulerpb.ReportSplitResponse")
	proto.RegisterType((*SplitID)(nil), "schedulerpb.SplitID")
	proto.RegisterType((*TimeInterval)(nil), "schedulerpb.TimeInterval")
	proto.RegisterType((*RecordPair)(nil), "schedulerpb.RecordPair")
	proto.RegisterType((*StoreStats)(nil), "schedulerpb.StoreStats")
	proto.RegisterType((*StoreHeartbeatRequest)(nil), "schedulerpb.StoreHeartbeatRequest")
	proto.RegisterType((*StoreHeartbeatResponse)(nil), "schedulerpb.StoreHeartbeatResponse")
	proto.RegisterType((*ScatterRegionRequest)(nil), "schedulerpb.ScatterRegionRequest")
	proto.RegisterType((*ScatterRegionResponse)(nil), "schedulerpb.ScatterRegionResponse")
	proto.RegisterType((*GetGCSafePointRequest)(nil), "schedulerpb.GetGCSafePointRequest")
	proto.RegisterType((*GetGCSafePointResponse)(nil), "schedulerpb.GetGCSafePointResponse")
	proto.RegisterType((*UpdateGCSafePointRequest)(nil), "schedulerpb.UpdateGCSafePointRequest")
	proto.RegisterType((*UpdateGCSafePointResponse)(nil), "schedulerpb.UpdateGCSafePointResponse")
	proto.RegisterType((*GetOperatorRequest)(nil), "schedulerpb.GetOperatorRequest")
	proto.RegisterType((*GetOperatorResponse)(nil), "schedulerpb.GetOperatorResponse")
	proto.RegisterEnum("schedulerpb.ErrorType", ErrorType_name, ErrorType_value)
	proto.RegisterEnum("schedulerpb.OperatorStatus", OperatorStatus_name, OperatorStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Scheduler service

type SchedulerClient interface {
	// GetMembers get the member list of this cluster. It does not require
	// the cluster_id in request matchs the id of this cluster.
	GetMembers(ctx context.Context, in *GetMembersRequest, opts ...grpc.CallOption) (*GetMembersResponse, error)
	Tso(ctx context.Context, opts ...grpc.CallOption) (Scheduler_TsoClient, error)
	Bootstrap(ctx context.Context, in *BootstrapRequest, opts ...grpc.CallOption) (*BootstrapResponse, error)
	IsBootstrapped(ctx context.Context, in *IsBootstrappedRequest, opts ...grpc.CallOption) (*IsBootstrappedResponse, error)
	AllocID(ctx context.Context, in *AllocIDRequest, opts ...grpc.CallOption) (*AllocIDResponse, error)
	GetStore(ctx context.Context, in *GetStoreRequest, opts ...grpc.CallOption) (*GetStoreResponse, error)
	PutStore(ctx context.Context, in *PutStoreRequest, opts ...grpc.CallOption) (*PutStoreResponse, error)
	GetAllStores(ctx context.Context, in *GetAllStoresRequest, opts ...grpc.CallOption) (*GetAllStoresResponse, error)
	StoreHeartbeat(ctx context.Context, in *StoreHeartbeatRequest, opts ...grpc.CallOption) (*StoreHeartbeatResponse, error)
	RegionHeartbeat(ctx context.Context, opts ...grpc.CallOption) (Scheduler_RegionHeartbeatClient, error)
	GetRegion(ctx context.Context, in *GetRegionRequest, opts ...grpc.CallOption) (*GetRegionResponse, error)
	GetPrevRegion(ctx context.Context, in *GetRegionRequest, opts ...grpc.CallOption) (*GetRegionResponse, error)
	GetRegionByID(ctx context.Context, in *GetRegionByIDRequest, opts ...grpc.CallOption) (*GetRegionResponse, error)
	ScanRegions(ctx context.Context, in *ScanRegionsRequest, opts ...grpc.CallOption) (*ScanRegionsResponse, error)
	AskSplit(ctx context.Context, in *AskSplitRequest, opts ...grpc.CallOption) (*AskSplitResponse, error)
	GetClusterConfig(ctx context.Context, in *GetClusterConfigRequest, opts ...grpc.CallOption) (*GetClusterConfigResponse, error)
	PutClusterConfig(ctx context.Context, in *PutClusterConfigRequest, opts ...grpc.CallOption) (*PutClusterConfigResponse, error)
	ScatterRegion(ctx context.Context, in *ScatterRegionRequest, opts ...grpc.CallOption) (*ScatterRegionResponse, error)
	GetGCSafePoint(ctx context.Context, in *GetGCSafePointRequest, opts ...grpc.CallOption) (*GetGCSafePointResponse, error)
	UpdateGCSafePoint(ctx context.Context, in *UpdateGCSafePointRequest, opts ...grpc.CallOption) (*UpdateGCSafePointResponse, error)
	GetOperator(ctx context.Context, in *GetOperatorRequest, opts ...grpc.CallOption) (*GetOperatorResponse, error)
}

type schedulerClient struct {
	cc *grpc.ClientConn
}

func NewSchedulerClient(cc *grpc.ClientConn) SchedulerClient {
	return &schedulerClient{cc}
}

func (c *schedulerClient) GetMembers(ctx context.Context, in *GetMembersRequest, opts ...grpc.CallOption) (*GetMembersResponse, error) {
	out := new(GetMembersResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/GetMembers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) Tso(ctx context.Context, opts ...grpc.CallOption) (Scheduler_TsoClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Scheduler_serviceDesc.Streams[0], "/schedulerpb.Scheduler/Tso", opts...)
	if err != nil {
		return nil, err
	}
	x := &schedulerTsoClient{stream}
	return x, nil
}

type Scheduler_TsoClient interface {
	Send(*TsoRequest) error
	Recv() (*TsoResponse, error)
	grpc.ClientStream
}

type schedulerTsoClient struct {
	grpc.ClientStream
}

func (x *schedulerTsoClient) Send(m *TsoRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *schedulerTsoClient) Recv() (*TsoResponse, error) {
	m := new(TsoResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *schedulerClient) Bootstrap(ctx context.Context, in *BootstrapRequest, opts ...grpc.CallOption) (*BootstrapResponse, error) {
	out := new(BootstrapResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/Bootstrap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) IsBootstrapped(ctx context.Context, in *IsBootstrappedRequest, opts ...grpc.CallOption) (*IsBootstrappedResponse, error) {
	out := new(IsBootstrappedResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/IsBootstrapped", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) AllocID(ctx context.Context, in *AllocIDRequest, opts ...grpc.CallOption) (*AllocIDResponse, error) {
	out := new(AllocIDResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/AllocID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) GetStore(ctx context.Context, in *GetStoreRequest, opts ...grpc.CallOption) (*GetStoreResponse, error) {
	out := new(GetStoreResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/GetStore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) PutStore(ctx context.Context, in *PutStoreRequest, opts ...grpc.CallOption) (*PutStoreResponse, error) {
	out := new(PutStoreResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/PutStore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) GetAllStores(ctx context.Context, in *GetAllStoresRequest, opts ...grpc.CallOption) (*GetAllStoresResponse, error) {
	out := new(GetAllStoresResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/GetAllStores", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) StoreHeartbeat(ctx context.Context, in *StoreHeartbeatRequest, opts ...grpc.CallOption) (*StoreHeartbeatResponse, error) {
	out := new(StoreHeartbeatResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/StoreHeartbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) RegionHeartbeat(ctx context.Context, opts ...grpc.CallOption) (Scheduler_RegionHeartbeatClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Scheduler_serviceDesc.Streams[1], "/schedulerpb.Scheduler/RegionHeartbeat", opts...)
	if err != nil {
		return nil, err
	}
	x := &schedulerRegionHeartbeatClient{stream}
	return x, nil
}

type Scheduler_RegionHeartbeatClient interface {
	Send(*RegionHeartbeatRequest) error
	Recv() (*RegionHeartbeatResponse, error)
	grpc.ClientStream
}

type schedulerRegionHeartbeatClient struct {
	grpc.ClientStream
}

func (x *schedulerRegionHeartbeatClient) Send(m *RegionHeartbeatRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *schedulerRegionHeartbeatClient) Recv() (*RegionHeartbeatResponse, error) {
	m := new(RegionHeartbeatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *schedulerClient) GetRegion(ctx context.Context, in *GetRegionRequest, opts ...grpc.CallOption) (*GetRegionResponse, error) {
	out := new(GetRegionResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/GetRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) GetPrevRegion(ctx context.Context, in *GetRegionRequest, opts ...grpc.CallOption) (*GetRegionResponse, error) {
	out := new(GetRegionResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/GetPrevRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) GetRegionByID(ctx context.Context, in *GetRegionByIDRequest, opts ...grpc.CallOption) (*GetRegionResponse, error) {
	out := new(GetRegionResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/GetRegionByID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) ScanRegions(ctx context.Context, in *ScanRegionsRequest, opts ...grpc.CallOption) (*ScanRegionsResponse, error) {
	out := new(ScanRegionsResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/ScanRegions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) AskSplit(ctx context.Context, in *AskSplitRequest, opts ...grpc.CallOption) (*AskSplitResponse, error) {
	out := new(AskSplitResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/AskSplit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) GetClusterConfig(ctx context.Context, in *GetClusterConfigRequest, opts ...grpc.CallOption) (*GetClusterConfigResponse, error) {
	out := new(GetClusterConfigResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/GetClusterConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) PutClusterConfig(ctx context.Context, in *PutClusterConfigRequest, opts ...grpc.CallOption) (*PutClusterConfigResponse, error) {
	out := new(PutClusterConfigResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/PutClusterConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) ScatterRegion(ctx context.Context, in *ScatterRegionRequest, opts ...grpc.CallOption) (*ScatterRegionResponse, error) {
	out := new(ScatterRegionResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/ScatterRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) GetGCSafePoint(ctx context.Context, in *GetGCSafePointRequest, opts ...grpc.CallOption) (*GetGCSafePointResponse, error) {
	out := new(GetGCSafePointResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/GetGCSafePoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) UpdateGCSafePoint(ctx context.Context, in *UpdateGCSafePointRequest, opts ...grpc.CallOption) (*UpdateGCSafePointResponse, error) {
	out := new(UpdateGCSafePointResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/UpdateGCSafePoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulerClient) GetOperator(ctx context.Context, in *GetOperatorRequest, opts ...grpc.CallOption) (*GetOperatorResponse, error) {
	out := new(GetOperatorResponse)
	err := c.cc.Invoke(ctx, "/schedulerpb.Scheduler/GetOperator", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Scheduler service

type SchedulerServer interface {
	// GetMembers get the member list of this cluster. It does not require
	// the cluster_id in request matchs the id of this cluster.
	GetMembers(context.Context, *GetMembersRequest) (*GetMembersResponse, error)
	Tso(Scheduler_TsoServer) error
	Bootstrap(context.Context, *BootstrapRequest) (*BootstrapResponse, error)
	IsBootstrapped(context.Context, *IsBootstrappedRequest) (*IsBootstrappedResponse, error)
	AllocID(context.Context, *AllocIDRequest) (*AllocIDResponse, error)
	GetStore(context.Context, *GetStoreRequest) (*GetStoreResponse, error)
	PutStore(context.Context, *PutStoreRequest) (*PutStoreResponse, error)
	GetAllStores(context.Context, *GetAllStoresRequest) (*GetAllStoresResponse, error)
	StoreHeartbeat(context.Context, *StoreHeartbeatRequest) (*StoreHeartbeatResponse, error)
	RegionHeartbeat(Scheduler_RegionHeartbeatServer) error
	GetRegion(context.Context, *GetRegionRequest) (*GetRegionResponse, error)
	GetPrevRegion(context.Context, *GetRegionRequest) (*GetRegionResponse, error)
	GetRegionByID(context.Context, *GetRegionByIDRequest) (*GetRegionResponse, error)
	ScanRegions(context.Context, *ScanRegionsRequest) (*ScanRegionsResponse, error)
	AskSplit(context.Context, *AskSplitRequest) (*AskSplitResponse, error)
	GetClusterConfig(context.Context, *GetClusterConfigRequest) (*GetClusterConfigResponse, error)
	PutClusterConfig(context.Context, *PutClusterConfigRequest) (*PutClusterConfigResponse, error)
	ScatterRegion(context.Context, *ScatterRegionRequest) (*ScatterRegionResponse, error)
	GetGCSafePoint(context.Context, *GetGCSafePointRequest) (*GetGCSafePointResponse, error)
	UpdateGCSafePoint(context.Context, *UpdateGCSafePointRequest) (*UpdateGCSafePointResponse, error)
	GetOperator(context.Context, *GetOperatorRequest) (*GetOperatorResponse, error)
}

func RegisterSchedulerServer(s *grpc.Server, srv SchedulerServer) {
	s.RegisterService(&_Scheduler_serviceDesc, srv)
}

func _Scheduler_GetMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).GetMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/GetMembers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).GetMembers(ctx, req.(*GetMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_Tso_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(SchedulerServer).Tso(&schedulerTsoServer{stream})
}

type Scheduler_TsoServer interface {
	Send(*TsoResponse) error
	Recv() (*TsoRequest, error)
	grpc.ServerStream
}

type schedulerTsoServer struct {
	grpc.ServerStream
}

func (x *schedulerTsoServer) Send(m *TsoResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *schedulerTsoServer) Recv() (*TsoRequest, error) {
	m := new(TsoRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Scheduler_Bootstrap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BootstrapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).Bootstrap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/Bootstrap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).Bootstrap(ctx, req.(*BootstrapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_IsBootstrapped_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsBootstrappedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).IsBootstrapped(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/IsBootstrapped",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).IsBootstrapped(ctx, req.(*IsBootstrappedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_AllocID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllocIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).AllocID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/AllocID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).AllocID(ctx, req.(*AllocIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_GetStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).GetStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/GetStore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).GetStore(ctx, req.(*GetStoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_PutStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutStoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).PutStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/PutStore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).PutStore(ctx, req.(*PutStoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_GetAllStores_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllStoresRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).GetAllStores(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/GetAllStores",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).GetAllStores(ctx, req.(*GetAllStoresRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_StoreHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreHeartbeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).StoreHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/StoreHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).StoreHeartbeat(ctx, req.(*StoreHeartbeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_RegionHeartbeat_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(SchedulerServer).RegionHeartbeat(&schedulerRegionHeartbeatServer{stream})
}

type Scheduler_RegionHeartbeatServer interface {
	Send(*RegionHeartbeatResponse) error
	Recv() (*RegionHeartbeatRequest, error)
	grpc.ServerStream
}

type schedulerRegionHeartbeatServer struct {
	grpc.ServerStream
}

func (x *schedulerRegionHeartbeatServer) Send(m *RegionHeartbeatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *schedulerRegionHeartbeatServer) Recv() (*RegionHeartbeatRequest, error) {
	m := new(RegionHeartbeatRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Scheduler_GetRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).GetRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/GetRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).GetRegion(ctx, req.(*GetRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_GetPrevRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).GetPrevRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/GetPrevRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).GetPrevRegion(ctx, req.(*GetRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_GetRegionByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRegionByIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).GetRegionByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/GetRegionByID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).GetRegionByID(ctx, req.(*GetRegionByIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_ScanRegions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanRegionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).ScanRegions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/ScanRegions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).ScanRegions(ctx, req.(*ScanRegionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_AskSplit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AskSplitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).AskSplit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/AskSplit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).AskSplit(ctx, req.(*AskSplitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_GetClusterConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClusterConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).GetClusterConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/GetClusterConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).GetClusterConfig(ctx, req.(*GetClusterConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_PutClusterConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutClusterConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).PutClusterConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/PutClusterConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).PutClusterConfig(ctx, req.(*PutClusterConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_ScatterRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScatterRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).ScatterRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/ScatterRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).ScatterRegion(ctx, req.(*ScatterRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_GetGCSafePoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGCSafePointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).GetGCSafePoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/GetGCSafePoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).GetGCSafePoint(ctx, req.(*GetGCSafePointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_UpdateGCSafePoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGCSafePointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).UpdateGCSafePoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/UpdateGCSafePoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).UpdateGCSafePoint(ctx, req.(*UpdateGCSafePointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduler_GetOperator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperatorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulerServer).GetOperator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulerpb.Scheduler/GetOperator",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulerServer).GetOperator(ctx, req.(*GetOperatorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Scheduler_serviceDesc = grpc.ServiceDesc{
	ServiceName: "schedulerpb.Scheduler",
	HandlerType: (*SchedulerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMembers",
			Handler:    _Scheduler_GetMembers_Handler,
		},
		{
			MethodName: "Bootstrap",
			Handler:    _Scheduler_Bootstrap_Handler,
		},
		{
			MethodName: "IsBootstrapped",
			Handler:    _Scheduler_IsBootstrapped_Handler,
		},
		{
			MethodName: "AllocID",
			Handler:    _Scheduler_AllocID_Handler,
		},
		{
			MethodName: "GetStore",
			Handler:    _Scheduler_GetStore_Handler,
		},
		{
			MethodName: "PutStore",
			Handler:    _Scheduler_PutStore_Handler,
		},
		{
			MethodName: "GetAllStores",
			Handler:    _Scheduler_GetAllStores_Handler,
		},
		{
			MethodName: "StoreHeartbeat",
			Handler:    _Scheduler_StoreHeartbeat_Handler,
		},
		{
			MethodName: "GetRegion",
			Handler:    _Scheduler_GetRegion_Handler,
		},
		{
			MethodName: "GetPrevRegion",
			Handler:    _Scheduler_GetPrevRegion_Handler,
		},
		{
			MethodName: "GetRegionByID",
			Handler:    _Scheduler_GetRegionByID_Handler,
		},
		{
			MethodName: "ScanRegions",
			Handler:    _Scheduler_ScanRegions_Handler,
		},
		{
			MethodName: "AskSplit",
			Handler:    _Scheduler_AskSplit_Handler,
		},
		{
			MethodName: "GetClusterConfig",
			Handler:    _Scheduler_GetClusterConfig_Handler,
		},
		{
			MethodName: "PutClusterConfig",
			Handler:    _Scheduler_PutClusterConfig_Handler,
		},
		{
			MethodName: "ScatterRegion",
			Handler:    _Scheduler_ScatterRegion_Handler,
		},
		{
			MethodName: "GetGCSafePoint",
			Handler:    _Scheduler_GetGCSafePoint_Handler,
		},
		{
			MethodName: "UpdateGCSafePoint",
			Handler:    _Scheduler_UpdateGCSafePoint_Handler,
		},
		{
			MethodName: "GetOperator",
			Handler:    _Scheduler_GetOperator_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Tso",
			Handler:       _Scheduler_Tso_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "RegionHeartbeat",
			Handler:       _Scheduler_RegionHeartbeat_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "schedulerpb.proto",
}

func (m *RequestHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RequestHeader) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ClusterId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.ClusterId))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ResponseHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResponseHeader) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ClusterId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.ClusterId))
	}
	if m.Error != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Error.Size()))
		n1, err := m.Error.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Type != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Type))
	}
	if len(m.Message) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(len(m.Message)))
		i += copy(dAtA[i:], m.Message)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *TsoRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TsoRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n2, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.Count != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Count))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Timestamp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Timestamp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Physical != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Physical))
	}
	if m.Logical != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Logical))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *TsoResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TsoResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n3, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.Count != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Count))
	}
	if m.Timestamp != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Timestamp.Size()))
		n4, err := m.Timestamp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *BootstrapRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BootstrapRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n5, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.Store != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Store.Size()))
		n6, err := m.Store.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *BootstrapResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BootstrapResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n7, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *IsBootstrappedRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IsBootstrappedRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n8, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *IsBootstrappedResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IsBootstrappedResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n9, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if m.Bootstrapped {
		dAtA[i] = 0x10
		i++
		if m.Bootstrapped {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *AllocIDRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllocIDRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n10, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *AllocIDResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AllocIDResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n11, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if m.Id != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Id))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetStoreRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStoreRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n12, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if m.StoreId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.StoreId))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetStoreResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStoreResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n13, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	if m.Store != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Store.Size()))
		n14, err := m.Store.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	if m.Stats != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Stats.Size()))
		n15, err := m.Stats.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *PutStoreRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutStoreRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n16, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	if m.Store != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Store.Size()))
		n17, err := m.Store.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *PutStoreResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutStoreResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n18, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetAllStoresRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllStoresRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n19, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	if m.ExcludeTombstoneStores {
		dAtA[i] = 0x10
		i++
		if m.ExcludeTombstoneStores {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetAllStoresResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllStoresResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n20, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	if len(m.Stores) > 0 {
		for _, msg := range m.Stores {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSchedulerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetRegionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRegionRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n21, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	if len(m.RegionKey) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(len(m.RegionKey)))
		i += copy(dAtA[i:], m.RegionKey)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetRegionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRegionResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n22, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	if m.Region != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Region.Size()))
		n23, err := m.Region.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	if m.Leader != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Leader.Size()))
		n24, err := m.Leader.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	if len(m.Slaves) > 0 {
		for _, msg := range m.Slaves {
			dAtA[i] = 0x22
			i++
			i = encodeVarintSchedulerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetRegionByIDRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRegionByIDRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n25, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	if m.RegionId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.RegionId))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ScanRegionsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScanRegionsRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n26, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	if len(m.StartKey) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(len(m.StartKey)))
		i += copy(dAtA[i:], m.StartKey)
	}
	if m.Limit != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Limit))
	}
	if len(m.EndKey) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(len(m.EndKey)))
		i += copy(dAtA[i:], m.EndKey)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ScanRegionsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScanRegionsResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n27, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	if len(m.Regions) > 0 {
		for _, msg := range m.Regions {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSchedulerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.Leaders) > 0 {
		for _, msg := range m.Leaders {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintSchedulerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetClusterConfigRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetClusterConfigRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n28, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetClusterConfigResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetClusterConfigResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n29, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	if m.Cluster != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Cluster.Size()))
		n30, err := m.Cluster.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n30
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *PutClusterConfigRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutClusterConfigRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n31, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n31
	}
	if m.Cluster != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Cluster.Size()))
		n32, err := m.Cluster.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n32
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *PutClusterConfigResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutClusterConfigResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n33, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n33
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Member) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Member) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Name) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(len(m.Name)))
		i += copy(dAtA[i:], m.Name)
	}
	if m.MemberId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.MemberId))
	}
	if len(m.PeerUrls) > 0 {
		for _, s := range m.PeerUrls {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.ClientUrls) > 0 {
		for _, s := range m.ClientUrls {
			dAtA[i] = 0x22
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if m.LeaderPriority != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.LeaderPriority))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetMembersRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMembersRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n34, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n34
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetMembersResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMembersResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n35, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n35
	}
	if len(m.Members) > 0 {
		for _, msg := range m.Members {
			dAtA[i] = 0x12
			i++
			i = encodeVarintSchedulerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Leader != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Leader.Size()))
		n36, err := m.Leader.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n36
	}
	if m.EtcdLeader != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.EtcdLeader.Size()))
		n37, err := m.EtcdLeader.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n37
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RegionHeartbeatRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionHeartbeatRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n38, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n38
	}
	if m.Region != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Region.Size()))
		n39, err := m.Region.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n39
	}
	if m.Leader != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Leader.Size()))
		n40, err := m.Leader.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n40
	}
	if len(m.PendingPeers) > 0 {
		for _, msg := range m.PendingPeers {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintSchedulerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.ApproximateSize != 0 {
		dAtA[i] = 0x50
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.ApproximateSize))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ChangePeer) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangePeer) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Peer != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Peer.Size()))
		n41, err := m.Peer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n41
	}
	if m.ChangeType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.ChangeType))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *TransferLeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferLeader) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Peer != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Peer.Size()))
		n42, err := m.Peer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n42
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RegionHeartbeatResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionHeartbeatResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n43, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n43
	}
	if m.ChangePeer != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.ChangePeer.Size()))
		n44, err := m.ChangePeer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n44
	}
	if m.TransferLeader != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.TransferLeader.Size()))
		n45, err := m.TransferLeader.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n45
	}
	if m.RegionId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.RegionId))
	}
	if m.RegionEpoch != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.RegionEpoch.Size()))
		n46, err := m.RegionEpoch.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n46
	}
	if m.TargetPeer != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.TargetPeer.Size()))
		n47, err := m.TargetPeer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n47
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *AskSplitRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AskSplitRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n48, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n48
	}
	if m.Region != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Region.Size()))
		n49, err := m.Region.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n49
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *AskSplitResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AskSplitResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n50, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n50
	}
	if m.NewRegionId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.NewRegionId))
	}
	if len(m.NewPeerIds) > 0 {
		dAtA52 := make([]byte, len(m.NewPeerIds)*10)
		var j51 int
		for _, num := range m.NewPeerIds {
			for num >= 1<<7 {
				dAtA52[j51] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j51++
			}
			dAtA52[j51] = uint8(num)
			j51++
		}
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(j51))
		i += copy(dAtA[i:], dAtA52[:j51])
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ReportSplitRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportSplitRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n53, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n53
	}
	if m.Left != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Left.Size()))
		n54, err := m.Left.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n54
	}
	if m.Right != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Right.Size()))
		n55, err := m.Right.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n55
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ReportSplitResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportSplitResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n56, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n56
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *SplitID) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SplitID) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.NewRegionId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.NewRegionId))
	}
	if len(m.NewPeerIds) > 0 {
		dAtA58 := make([]byte, len(m.NewPeerIds)*10)
		var j57 int
		for _, num := range m.NewPeerIds {
			for num >= 1<<7 {
				dAtA58[j57] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j57++
			}
			dAtA58[j57] = uint8(num)
			j57++
		}
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(j57))
		i += copy(dAtA[i:], dAtA58[:j57])
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *TimeInterval) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TimeInterval) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.StartTimestamp != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.StartTimestamp))
	}
	if m.EndTimestamp != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.EndTimestamp))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RecordPair) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordPair) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Key) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(len(m.Key)))
		i += copy(dAtA[i:], m.Key)
	}
	if m.Value != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Value))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *StoreStats) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreStats) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.StoreId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.StoreId))
	}
	if m.Capacity != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Capacity))
	}
	if m.Available != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Available))
	}
	if m.RegionCount != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.RegionCount))
	}
	if m.SendingSnapCount != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.SendingSnapCount))
	}
	if m.ReceivingSnapCount != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.ReceivingSnapCount))
	}
	if m.StartTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.StartTime))
	}
	if m.ApplyingSnapCount != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.ApplyingSnapCount))
	}
	if m.IsBusy {
		dAtA[i] = 0x48
		i++
		if m.IsBusy {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.UsedSize != 0 {
		dAtA[i] = 0x50
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.UsedSize))
	}
	if m.Interval != nil {
		dAtA[i] = 0x7a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Interval.Size()))
		n59, err := m.Interval.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n59
	}
	if len(m.CpuUsages) > 0 {
		for _, msg := range m.CpuUsages {
			dAtA[i] = 0x82
			i++
			dAtA[i] = 0x1
			i++
			i = encodeVarintSchedulerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.ReadIoRates) > 0 {
		for _, msg := range m.ReadIoRates {
			dAtA[i] = 0x8a
			i++
			dAtA[i] = 0x1
			i++
			i = encodeVarintSchedulerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.WriteIoRates) > 0 {
		for _, msg := range m.WriteIoRates {
			dAtA[i] = 0x92
			i++
			dAtA[i] = 0x1
			i++
			i = encodeVarintSchedulerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.OpLatencies) > 0 {
		for _, msg := range m.OpLatencies {
			dAtA[i] = 0x9a
			i++
			dAtA[i] = 0x1
			i++
			i = encodeVarintSchedulerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *StoreHeartbeatRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreHeartbeatRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n60, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n60
	}
	if m.Stats != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Stats.Size()))
		n61, err := m.Stats.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n61
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *StoreHeartbeatResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreHeartbeatResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n62, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n62
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ScatterRegionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScatterRegionRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n63, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n63
	}
	if m.RegionId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.RegionId))
	}
	if m.Region != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Region.Size()))
		n64, err := m.Region.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n64
	}
	if m.Leader != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Leader.Size()))
		n65, err := m.Leader.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n65
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ScatterRegionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScatterRegionResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n66, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n66
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetGCSafePointRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGCSafePointRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n67, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n67
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetGCSafePointResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGCSafePointResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n68, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n68
	}
	if m.SafePoint != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.SafePoint))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *UpdateGCSafePointRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGCSafePointRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n69, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n69
	}
	if m.SafePoint != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.SafePoint))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *UpdateGCSafePointResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGCSafePointResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n70, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n70
	}
	if m.NewSafePoint != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.NewSafePoint))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetOperatorRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperatorRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n71, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n71
	}
	if m.RegionId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.RegionId))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetOperatorResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperatorResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Header.Size()))
		n72, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n72
	}
	if m.RegionId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.RegionId))
	}
	if len(m.Desc) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(len(m.Desc)))
		i += copy(dAtA[i:], m.Desc)
	}
	if m.Status != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(m.Status))
	}
	if len(m.Kind) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintSchedulerpb(dAtA, i, uint64(len(m.Kind)))
		i += copy(dAtA[i:], m.Kind)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func encodeVarintSchedulerpb(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *RequestHeader) Size() (n int) {
	var l int
	_ = l
	if m.ClusterId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.ClusterId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ResponseHeader) Size() (n int) {
	var l int
	_ = l
	if m.ClusterId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.ClusterId))
	}
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Error) Size() (n int) {
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Type))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *TsoRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Count != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Count))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Timestamp) Size() (n int) {
	var l int
	_ = l
	if m.Physical != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Physical))
	}
	if m.Logical != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Logical))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *TsoResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Count != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Count))
	}
	if m.Timestamp != nil {
		l = m.Timestamp.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *BootstrapRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Store != nil {
		l = m.Store.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *BootstrapResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *IsBootstrappedRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *IsBootstrappedResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Bootstrapped {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *AllocIDRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *AllocIDResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Id != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Id))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetStoreRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.StoreId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetStoreResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Store != nil {
		l = m.Store.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Stats != nil {
		l = m.Stats.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PutStoreRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Store != nil {
		l = m.Store.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PutStoreResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetAllStoresRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.ExcludeTombstoneStores {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetAllStoresResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if len(m.Stores) > 0 {
		for _, e := range m.Stores {
			l = e.Size()
			n += 1 + l + sovSchedulerpb(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetRegionRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	l = len(m.RegionKey)
	if l > 0 {
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetRegionResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Leader != nil {
		l = m.Leader.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if len(m.Slaves) > 0 {
		for _, e := range m.Slaves {
			l = e.Size()
			n += 1 + l + sovSchedulerpb(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetRegionByIDRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.RegionId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ScanRegionsRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Limit != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Limit))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ScanRegionsResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if len(m.Regions) > 0 {
		for _, e := range m.Regions {
			l = e.Size()
			n += 1 + l + sovSchedulerpb(uint64(l))
		}
	}
	if len(m.Leaders) > 0 {
		for _, e := range m.Leaders {
			l = e.Size()
			n += 1 + l + sovSchedulerpb(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetClusterConfigRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetClusterConfigResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Cluster != nil {
		l = m.Cluster.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PutClusterConfigRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Cluster != nil {
		l = m.Cluster.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PutClusterConfigResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Member) Size() (n int) {
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.MemberId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.MemberId))
	}
	if len(m.PeerUrls) > 0 {
		for _, s := range m.PeerUrls {
			l = len(s)
			n += 1 + l + sovSchedulerpb(uint64(l))
		}
	}
	if len(m.ClientUrls) > 0 {
		for _, s := range m.ClientUrls {
			l = len(s)
			n += 1 + l + sovSchedulerpb(uint64(l))
		}
	}
	if m.LeaderPriority != 0 {
		n += 1 + sovSchedulerpb(uint64(m.LeaderPriority))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetMembersRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetMembersResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if len(m.Members) > 0 {
		for _, e := range m.Members {
			l = e.Size()
			n += 1 + l + sovSchedulerpb(uint64(l))
		}
	}
	if m.Leader != nil {
		l = m.Leader.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.EtcdLeader != nil {
		l = m.EtcdLeader.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RegionHeartbeatRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Leader != nil {
		l = m.Leader.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if len(m.PendingPeers) > 0 {
		for _, e := range m.PendingPeers {
			l = e.Size()
			n += 1 + l + sovSchedulerpb(uint64(l))
		}
	}
	if m.ApproximateSize != 0 {
		n += 1 + sovSchedulerpb(uint64(m.ApproximateSize))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ChangePeer) Size() (n int) {
	var l int
	_ = l
	if m.Peer != nil {
		l = m.Peer.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.ChangeType != 0 {
		n += 1 + sovSchedulerpb(uint64(m.ChangeType))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *TransferLeader) Size() (n int) {
	var l int
	_ = l
	if m.Peer != nil {
		l = m.Peer.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RegionHeartbeatResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.ChangePeer != nil {
		l = m.ChangePeer.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.TransferLeader != nil {
		l = m.TransferLeader.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.RegionId))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.TargetPeer != nil {
		l = m.TargetPeer.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *AskSplitRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *AskSplitResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.NewRegionId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.NewRegionId))
	}
	if len(m.NewPeerIds) > 0 {
		l = 0
		for _, e := range m.NewPeerIds {
			l += sovSchedulerpb(uint64(e))
		}
		n += 1 + sovSchedulerpb(uint64(l)) + l
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ReportSplitRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Left != nil {
		l = m.Left.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Right != nil {
		l = m.Right.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ReportSplitResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SplitID) Size() (n int) {
	var l int
	_ = l
	if m.NewRegionId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.NewRegionId))
	}
	if len(m.NewPeerIds) > 0 {
		l = 0
		for _, e := range m.NewPeerIds {
			l += sovSchedulerpb(uint64(e))
		}
		n += 1 + sovSchedulerpb(uint64(l)) + l
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *TimeInterval) Size() (n int) {
	var l int
	_ = l
	if m.StartTimestamp != 0 {
		n += 1 + sovSchedulerpb(uint64(m.StartTimestamp))
	}
	if m.EndTimestamp != 0 {
		n += 1 + sovSchedulerpb(uint64(m.EndTimestamp))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RecordPair) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Value != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Value))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StoreStats) Size() (n int) {
	var l int
	_ = l
	if m.StoreId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.StoreId))
	}
	if m.Capacity != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Capacity))
	}
	if m.Available != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Available))
	}
	if m.RegionCount != 0 {
		n += 1 + sovSchedulerpb(uint64(m.RegionCount))
	}
	if m.SendingSnapCount != 0 {
		n += 1 + sovSchedulerpb(uint64(m.SendingSnapCount))
	}
	if m.ReceivingSnapCount != 0 {
		n += 1 + sovSchedulerpb(uint64(m.ReceivingSnapCount))
	}
	if m.StartTime != 0 {
		n += 1 + sovSchedulerpb(uint64(m.StartTime))
	}
	if m.ApplyingSnapCount != 0 {
		n += 1 + sovSchedulerpb(uint64(m.ApplyingSnapCount))
	}
	if m.IsBusy {
		n += 2
	}
	if m.UsedSize != 0 {
		n += 1 + sovSchedulerpb(uint64(m.UsedSize))
	}
	if m.Interval != nil {
		l = m.Interval.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if len(m.CpuUsages) > 0 {
		for _, e := range m.CpuUsages {
			l = e.Size()
			n += 2 + l + sovSchedulerpb(uint64(l))
		}
	}
	if len(m.ReadIoRates) > 0 {
		for _, e := range m.ReadIoRates {
			l = e.Size()
			n += 2 + l + sovSchedulerpb(uint64(l))
		}
	}
	if len(m.WriteIoRates) > 0 {
		for _, e := range m.WriteIoRates {
			l = e.Size()
			n += 2 + l + sovSchedulerpb(uint64(l))
		}
	}
	if len(m.OpLatencies) > 0 {
		for _, e := range m.OpLatencies {
			l = e.Size()
			n += 2 + l + sovSchedulerpb(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StoreHeartbeatRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Stats != nil {
		l = m.Stats.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StoreHeartbeatResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ScatterRegionRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.RegionId))
	}
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Leader != nil {
		l = m.Leader.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ScatterRegionResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetGCSafePointRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetGCSafePointResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.SafePoint != 0 {
		n += 1 + sovSchedulerpb(uint64(m.SafePoint))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *UpdateGCSafePointRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.SafePoint != 0 {
		n += 1 + sovSchedulerpb(uint64(m.SafePoint))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *UpdateGCSafePointResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.NewSafePoint != 0 {
		n += 1 + sovSchedulerpb(uint64(m.NewSafePoint))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetOperatorRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.RegionId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetOperatorResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovSchedulerpb(uint64(m.RegionId))
	}
	l = len(m.Desc)
	if l > 0 {
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.Status != 0 {
		n += 1 + sovSchedulerpb(uint64(m.Status))
	}
	l = len(m.Kind)
	if l > 0 {
		n += 1 + l + sovSchedulerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovSchedulerpb(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozSchedulerpb(x uint64) (n int) {
	return sovSchedulerpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RequestHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RequestHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RequestHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterId", wireType)
			}
			m.ClusterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClusterId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResponseHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResponseHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResponseHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterId", wireType)
			}
			m.ClusterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClusterId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (ErrorType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TsoRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TsoRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TsoRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Timestamp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Timestamp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Timestamp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Physical", wireType)
			}
			m.Physical = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Physical |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Logical", wireType)
			}
			m.Logical = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Logical |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TsoResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TsoResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TsoResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Timestamp == nil {
				m.Timestamp = &Timestamp{}
			}
			if err := m.Timestamp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BootstrapRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BootstrapRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BootstrapRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Store", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Store == nil {
				m.Store = &metapb.Store{}
			}
			if err := m.Store.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BootstrapResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BootstrapResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BootstrapResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IsBootstrappedRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IsBootstrappedRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IsBootstrappedRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IsBootstrappedResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IsBootstrappedResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IsBootstrappedResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bootstrapped", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Bootstrapped = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AllocIDRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AllocIDRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AllocIDRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AllocIDResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AllocIDResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AllocIDResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStoreRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetStoreRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetStoreRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStoreResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetStoreResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetStoreResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Store", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Store == nil {
				m.Store = &metapb.Store{}
			}
			if err := m.Store.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Stats", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Stats == nil {
				m.Stats = &StoreStats{}
			}
			if err := m.Stats.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PutStoreRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutStoreRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutStoreRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Store", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Store == nil {
				m.Store = &metapb.Store{}
			}
			if err := m.Store.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PutStoreResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutStoreResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutStoreResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllStoresRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAllStoresRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAllStoresRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExcludeTombstoneStores", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ExcludeTombstoneStores = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllStoresResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAllStoresResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAllStoresResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Stores", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Stores = append(m.Stores, &metapb.Store{})
			if err := m.Stores[len(m.Stores)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRegionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRegionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRegionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RegionKey = append(m.RegionKey[:0], dAtA[iNdEx:postIndex]...)
			if m.RegionKey == nil {
				m.RegionKey = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRegionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRegionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRegionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Leader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Leader == nil {
				m.Leader = &metapb.Peer{}
			}
			if err := m.Leader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Slaves", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Slaves = append(m.Slaves, &metapb.Peer{})
			if err := m.Slaves[len(m.Slaves)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRegionByIDRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRegionByIDRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRegionByIDRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScanRegionsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ScanRegionsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ScanRegionsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScanRegionsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ScanRegionsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ScanRegionsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Regions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Regions = append(m.Regions, &metapb.Region{})
			if err := m.Regions[len(m.Regions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Leaders", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Leaders = append(m.Leaders, &metapb.Peer{})
			if err := m.Leaders[len(m.Leaders)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetClusterConfigRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetClusterConfigRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetClusterConfigRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetClusterConfigResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetClusterConfigResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetClusterConfigResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cluster", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Cluster == nil {
				m.Cluster = &metapb.Cluster{}
			}
			if err := m.Cluster.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PutClusterConfigRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutClusterConfigRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutClusterConfigRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cluster", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Cluster == nil {
				m.Cluster = &metapb.Cluster{}
			}
			if err := m.Cluster.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PutClusterConfigResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutClusterConfigResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutClusterConfigResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Member) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Member: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Member: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MemberId", wireType)
			}
			m.MemberId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PeerUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PeerUrls = append(m.PeerUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientUrls = append(m.ClientUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LeaderPriority", wireType)
			}
			m.LeaderPriority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeaderPriority |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMembersRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetMembersRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetMembersRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMembersResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetMembersResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetMembersResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Members", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Members = append(m.Members, &Member{})
			if err := m.Members[len(m.Members)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Leader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Leader == nil {
				m.Leader = &Member{}
			}
			if err := m.Leader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EtcdLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.EtcdLeader == nil {
				m.EtcdLeader = &Member{}
			}
			if err := m.EtcdLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionHeartbeatRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionHeartbeatRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionHeartbeatRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Leader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Leader == nil {
				m.Leader = &metapb.Peer{}
			}
			if err := m.Leader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PendingPeers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PendingPeers = append(m.PendingPeers, &metapb.Peer{})
			if err := m.PendingPeers[len(m.PendingPeers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApproximateSize", wireType)
			}
			m.ApproximateSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApproximateSize |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangePeer) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChangePeer: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChangePeer: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Peer == nil {
				m.Peer = &metapb.Peer{}
			}
			if err := m.Peer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangeType", wireType)
			}
			m.ChangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeType |= (eraftpb.ConfChangeType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferLeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TransferLeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TransferLeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Peer == nil {
				m.Peer = &metapb.Peer{}
			}
			if err := m.Peer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionHeartbeatResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionHeartbeatResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionHeartbeatResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangePeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ChangePeer == nil {
				m.ChangePeer = &ChangePeer{}
			}
			if err := m.ChangePeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TransferLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TransferLeader == nil {
				m.TransferLeader = &TransferLeader{}
			}
			if err := m.TransferLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetPeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TargetPeer == nil {
				m.TargetPeer = &metapb.Peer{}
			}
			if err := m.TargetPeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AskSplitRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AskSplitRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AskSplitRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AskSplitResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AskSplitResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AskSplitResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewRegionId", wireType)
			}
			m.NewRegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewRegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSchedulerpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.NewPeerIds = append(m.NewPeerIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSchedulerpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSchedulerpb
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSchedulerpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.NewPeerIds = append(m.NewPeerIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field NewPeerIds", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportSplitRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReportSplitRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReportSplitRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Left", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Left == nil {
				m.Left = &metapb.Region{}
			}
			if err := m.Left.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Right", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Right == nil {
				m.Right = &metapb.Region{}
			}
			if err := m.Right.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportSplitResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReportSplitResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReportSplitResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SplitID) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SplitID: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SplitID: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewRegionId", wireType)
			}
			m.NewRegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewRegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSchedulerpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.NewPeerIds = append(m.NewPeerIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSchedulerpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSchedulerpb
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSchedulerpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.NewPeerIds = append(m.NewPeerIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field NewPeerIds", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TimeInterval) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TimeInterval: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TimeInterval: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimestamp", wireType)
			}
			m.StartTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestamp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTimestamp", wireType)
			}
			m.EndTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTimestamp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordPair) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RecordPair: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RecordPair: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreStats) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreStats: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreStats: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Capacity", wireType)
			}
			m.Capacity = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Capacity |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Available", wireType)
			}
			m.Available = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Available |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionCount", wireType)
			}
			m.RegionCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SendingSnapCount", wireType)
			}
			m.SendingSnapCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendingSnapCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReceivingSnapCount", wireType)
			}
			m.ReceivingSnapCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReceivingSnapCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApplyingSnapCount", wireType)
			}
			m.ApplyingSnapCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyingSnapCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsBusy", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBusy = bool(v != 0)
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UsedSize", wireType)
			}
			m.UsedSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UsedSize |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Interval", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Interval == nil {
				m.Interval = &TimeInterval{}
			}
			if err := m.Interval.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CpuUsages", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CpuUsages = append(m.CpuUsages, &RecordPair{})
			if err := m.CpuUsages[len(m.CpuUsages)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadIoRates", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReadIoRates = append(m.ReadIoRates, &RecordPair{})
			if err := m.ReadIoRates[len(m.ReadIoRates)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WriteIoRates", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WriteIoRates = append(m.WriteIoRates, &RecordPair{})
			if err := m.WriteIoRates[len(m.WriteIoRates)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OpLatencies", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OpLatencies = append(m.OpLatencies, &RecordPair{})
			if err := m.OpLatencies[len(m.OpLatencies)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreHeartbeatRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreHeartbeatRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreHeartbeatRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Stats", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Stats == nil {
				m.Stats = &StoreStats{}
			}
			if err := m.Stats.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreHeartbeatResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreHeartbeatResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreHeartbeatResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScatterRegionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ScatterRegionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ScatterRegionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Leader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Leader == nil {
				m.Leader = &metapb.Peer{}
			}
			if err := m.Leader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScatterRegionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ScatterRegionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ScatterRegionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGCSafePointRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetGCSafePointRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetGCSafePointRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGCSafePointResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetGCSafePointResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetGCSafePointResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SafePoint", wireType)
			}
			m.SafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SafePoint |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGCSafePointRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateGCSafePointRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateGCSafePointRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SafePoint", wireType)
			}
			m.SafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SafePoint |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGCSafePointResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateGCSafePointResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateGCSafePointResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewSafePoint", wireType)
			}
			m.NewSafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewSafePoint |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperatorRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetOperatorRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetOperatorRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperatorResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetOperatorResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetOperatorResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Desc = append(m.Desc[:0], dAtA[iNdEx:postIndex]...)
			if m.Desc == nil {
				m.Desc = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (OperatorStatus(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Kind", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Kind = append(m.Kind[:0], dAtA[iNdEx:postIndex]...)
			if m.Kind == nil {
				m.Kind = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSchedulerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipSchedulerpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSchedulerpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSchedulerpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthSchedulerpb
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowSchedulerpb
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipSchedulerpb(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthSchedulerpb = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSchedulerpb   = fmt.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("schedulerpb.proto", fileDescriptor_schedulerpb_4e333137f5959f12) }

var fileDescriptor_schedulerpb_4e333137f5959f12 = []byte{
	// 2343 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0xcf, 0x6f, 0x23, 0x49,
	0xf5, 0x4f, 0x3b, 0x8e, 0x63, 0x3f, 0x3b, 0xb6, 0x53, 0xc9, 0x26, 0x5e, 0xef, 0x4c, 0x36, 0x53,
	0x33, 0x3b, 0xdf, 0xd9, 0xf9, 0x32, 0x61, 0xc9, 0x0e, 0xab, 0x15, 0x08, 0xa4, 0xc4, 0xf1, 0x66,
	0xcc, 0x24, 0xb6, 0xd5, 0x76, 0x06, 0x56, 0x20, 0x35, 0x1d, 0x77, 0xc5, 0x69, 0xa6, 0xdd, 0xdd,
	0xdb, 0x55, 0xce, 0x8c, 0xe7, 0xca, 0x89, 0x03, 0x08, 0x21, 0x90, 0x90, 0xe0, 0xc0, 0x3f, 0xc1,
	0x8d, 0x23, 0x07, 0x8e, 0x88, 0x2b, 0x17, 0x34, 0xfc, 0x13, 0x1c, 0x51, 0x55, 0x75, 0xb7, 0xbb,
	0xdb, 0x76, 0x12, 0xd4, 0x03, 0x37, 0x57, 0xbd, 0x4f, 0xbd, 0xdf, 0x55, 0xf5, 0xea, 0xb5, 0x61,
	0x9d, 0x0e, 0x2e, 0x89, 0x31, 0xb6, 0x88, 0xe7, 0x9e, 0xef, 0xb9, 0x9e, 0xc3, 0x1c, 0x54, 0x8c,
	0x4c, 0xd5, 0x4b, 0x23, 0xc2, 0xf4, 0x80, 0x54, 0x5f, 0x23, 0x9e, 0x7e, 0xc1, 0xc2, 0xe1, 0xe6,
	0xd0, 0x19, 0x3a, 0xe2, 0xe7, 0xd7, 0xf9, 0x2f, 0x39, 0x8b, 0xf7, 0x60, 0x4d, 0x25, 0x5f, 0x8d,
	0x09, 0x65, 0xcf, 0x88, 0x6e, 0x10, 0x0f, 0xdd, 0x05, 0x18, 0x58, 0x63, 0xca, 0x88, 0xa7, 0x99,
	0x46, 0x4d, 0xd9, 0x55, 0x1e, 0x65, 0xd5, 0x82, 0x3f, 0xd3, 0x32, 0xf0, 0x97, 0x50, 0x56, 0x09,
	0x75, 0x1d, 0x9b, 0x92, 0x5b, 0x2d, 0x40, 0x8f, 0x60, 0x85, 0x78, 0x9e, 0xe3, 0xd5, 0x32, 0xbb,
	0xca, 0xa3, 0xe2, 0x3e, 0xda, 0x8b, 0xda, 0xd0, 0xe4, 0x14, 0x55, 0x02, 0xf0, 0x29, 0xac, 0x88,
	0x31, 0x7a, 0x0c, 0x59, 0x36, 0x71, 0x89, 0xe0, 0x55, 0xde, 0xdf, 0x9a, 0x5d, 0xd1, 0x9f, 0xb8,
	0x44, 0x15, 0x18, 0x54, 0x83, 0xd5, 0x11, 0xa1, 0x54, 0x1f, 0x12, 0x21, 0xa0, 0xa0, 0x06, 0x43,
	0xfc, 0x02, 0xa0, 0x4f, 0x1d, 0xdf, 0x38, 0xb4, 0x0f, 0xb9, 0x4b, 0xa1, 0xaf, 0xe0, 0x5a, 0xdc,
	0xaf, 0xc7, 0xb8, 0xc6, 0x5c, 0xa0, 0xfa, 0x48, 0xb4, 0x09, 0x2b, 0x03, 0x67, 0x6c, 0x33, 0xc1,
	0x79, 0x4d, 0x95, 0x03, 0x7c, 0x00, 0x85, 0xbe, 0x39, 0x22, 0x94, 0xe9, 0x23, 0x17, 0xd5, 0x21,
	0xef, 0x5e, 0x4e, 0xa8, 0x39, 0xd0, 0x2d, 0xc1, 0x78, 0x59, 0x0d, 0xc7, 0x5c, 0x35, 0xcb, 0x19,
	0x0a, 0x52, 0x46, 0x90, 0x82, 0x21, 0xfe, 0x85, 0x02, 0x45, 0xa1, 0x9b, 0x74, 0x24, 0xfa, 0x34,
	0xa1, 0xdc, 0x07, 0x09, 0xe5, 0xa2, 0xfe, 0xbe, 0x5e, 0x3b, 0xf4, 0x14, 0x0a, 0x2c, 0xd0, 0xae,
	0xb6, 0x2c, 0xb8, 0xc5, 0x1d, 0x18, 0xea, 0xae, 0x4e, 0x81, 0xf8, 0x25, 0x54, 0x0f, 0x1d, 0x87,
	0x51, 0xe6, 0xe9, 0x6e, 0x1a, 0x8f, 0xdd, 0x87, 0x15, 0xca, 0x1c, 0x8f, 0xf8, 0xc1, 0x5e, 0xdb,
	0xf3, 0x13, 0xb2, 0xc7, 0x27, 0x55, 0x49, 0xc3, 0xcf, 0x60, 0x3d, 0x22, 0x2c, 0x85, 0x0b, 0xf0,
	0x73, 0x78, 0xaf, 0x45, 0x43, 0x5e, 0x2e, 0x31, 0x52, 0xe8, 0x8e, 0xbf, 0x82, 0xad, 0x24, 0xb3,
	0x34, 0xe1, 0xc1, 0x50, 0x3a, 0x8f, 0x30, 0x13, 0x1e, 0xc9, 0xab, 0xb1, 0x39, 0x7c, 0x04, 0xe5,
	0x03, 0xcb, 0x72, 0x06, 0xad, 0xa3, 0x34, 0x8a, 0xbf, 0x80, 0x4a, 0xc8, 0x25, 0x8d, 0xc6, 0x65,
	0xc8, 0x98, 0x52, 0xcf, 0xac, 0x9a, 0x31, 0x0d, 0xfc, 0x63, 0xa8, 0x1c, 0x13, 0x26, 0x43, 0x97,
	0x22, 0x27, 0xde, 0x87, 0xbc, 0x88, 0xbb, 0x16, 0x32, 0x5f, 0x15, 0xe3, 0x96, 0x81, 0x7f, 0xa7,
	0x40, 0x75, 0x2a, 0x22, 0x8d, 0xee, 0xb7, 0x49, 0x3c, 0xf4, 0x84, 0x83, 0x74, 0x46, 0xfd, 0x7d,
	0xb1, 0x1d, 0x63, 0x2c, 0x90, 0x3d, 0x4e, 0x56, 0x25, 0x0a, 0xff, 0x04, 0x2a, 0xdd, 0x71, 0x7a,
	0xfb, 0x6f, 0xb5, 0x27, 0x8e, 0xa1, 0x3a, 0x95, 0x95, 0x66, 0x4b, 0xfc, 0x54, 0x81, 0x8d, 0x63,
	0xc2, 0x0e, 0x2c, 0x4b, 0x30, 0xa3, 0x69, 0x34, 0xff, 0x1c, 0x6a, 0xe4, 0xf5, 0xc0, 0x1a, 0x1b,
	0x44, 0x63, 0xce, 0xe8, 0x9c, 0x32, 0xc7, 0x26, 0x9a, 0xd0, 0x97, 0xfa, 0xe9, 0xbc, 0xe5, 0xd3,
	0xfb, 0x01, 0x59, 0x0a, 0xc5, 0x1e, 0x6c, 0xc6, 0x95, 0x48, 0x13, 0xdb, 0x8f, 0x20, 0x17, 0x0a,
	0x5d, 0x9e, 0xf5, 0xa0, 0x4f, 0xc4, 0x44, 0xe4, 0x92, 0x4a, 0x86, 0xa6, 0x63, 0xa7, 0xb1, 0xfa,
	0x2e, 0x80, 0x27, 0x98, 0x68, 0x2f, 0xc9, 0x44, 0xd8, 0x59, 0x52, 0x0b, 0x72, 0xe6, 0x39, 0x99,
	0xe0, 0x3f, 0x29, 0xb0, 0x1e, 0x91, 0x93, 0xc6, 0xb0, 0x87, 0x90, 0x93, 0x7c, 0xfd, 0xd4, 0x28,
	0x07, 0x86, 0xf9, 0xcc, 0x7d, 0x2a, 0x7a, 0x00, 0x39, 0x4b, 0x32, 0x97, 0x89, 0x5b, 0x0a, 0x70,
	0x5d, 0xc2, 0xb9, 0x49, 0x1a, 0x47, 0x51, 0x4b, 0xbf, 0x22, 0xb4, 0x96, 0x15, 0x6e, 0x4a, 0xa0,
	0x24, 0x0d, 0x0f, 0x45, 0x64, 0xa4, 0x80, 0xc3, 0x49, 0xaa, 0x83, 0x07, 0x7d, 0x00, 0xbe, 0x5f,
	0xa6, 0x5b, 0x3b, 0x2f, 0x27, 0x5a, 0x06, 0xfe, 0xb5, 0x02, 0xa8, 0x37, 0xd0, 0x6d, 0x29, 0x8a,
	0xa6, 0x94, 0x43, 0x99, 0xee, 0xb1, 0x48, 0x40, 0xf2, 0x62, 0xe2, 0x39, 0x99, 0xf0, 0x6b, 0xd0,
	0x32, 0x47, 0x26, 0x13, 0xbe, 0x59, 0x51, 0xe5, 0x00, 0x6d, 0xc3, 0x2a, 0xb1, 0x0d, 0xb1, 0x20,
	0x2b, 0x16, 0xe4, 0x88, 0x6d, 0xf0, 0xf0, 0xfd, 0x5e, 0x81, 0x8d, 0x98, 0x5a, 0x69, 0x02, 0xf8,
	0x08, 0x56, 0xa5, 0xbd, 0x41, 0x6a, 0x26, 0x23, 0x18, 0x90, 0xd1, 0x43, 0x58, 0x95, 0x61, 0xe2,
	0x87, 0xcf, 0x6c, 0x74, 0x02, 0x22, 0x3e, 0x85, 0xed, 0x63, 0xc2, 0x1a, 0xb2, 0x7a, 0x6a, 0x38,
	0xf6, 0x85, 0x39, 0x4c, 0x73, 0x35, 0xbc, 0x81, 0xda, 0x2c, 0xbb, 0x34, 0x16, 0x7f, 0x0c, 0xab,
	0x7e, 0x69, 0xe7, 0xe7, 0x6c, 0x25, 0xb0, 0xc3, 0x17, 0xa2, 0x06, 0x74, 0xfc, 0x1a, 0xb6, 0xbb,
	0xe3, 0x77, 0x66, 0xca, 0x7f, 0x22, 0xb9, 0x03, 0xb5, 0x59, 0xc9, 0x69, 0x0e, 0xd5, 0x3f, 0x28,
	0x90, 0x3b, 0x25, 0xa3, 0x73, 0xe2, 0x21, 0x04, 0x59, 0x5b, 0x1f, 0xc9, 0xda, 0xb4, 0xa0, 0x8a,
	0xdf, 0x3c, 0x3f, 0x47, 0x82, 0x1a, 0xd9, 0x07, 0x72, 0xa2, 0x65, 0x70, 0xa2, 0x4b, 0x88, 0xa7,
	0x8d, 0x3d, 0x4b, 0xc6, 0xbe, 0xa0, 0xe6, 0xf9, 0xc4, 0x99, 0x67, 0x51, 0xf4, 0x21, 0x14, 0x07,
	0x96, 0x49, 0x6c, 0x26, 0xc9, 0x59, 0x41, 0x06, 0x39, 0x25, 0x00, 0xff, 0x07, 0x15, 0x99, 0x1a,
	0x9a, 0xeb, 0x99, 0x8e, 0x67, 0xb2, 0x49, 0x6d, 0x45, 0xe4, 0x79, 0x59, 0x4e, 0x77, 0xfd, 0x59,
	0x7c, 0x2c, 0x4e, 0x25, 0xa9, 0x64, 0x9a, 0xcd, 0x86, 0xff, 0xae, 0x00, 0x8a, 0x72, 0x4a, 0x93,
	0x2d, 0x4f, 0x78, 0x71, 0x2e, 0xf8, 0xf8, 0xfb, 0x63, 0x23, 0xb6, 0x4a, 0xca, 0x50, 0x03, 0x0c,
	0xfa, 0xff, 0xc4, 0x39, 0x37, 0x17, 0x1d, 0x1c, 0x77, 0x4f, 0xa1, 0x48, 0xd8, 0xc0, 0xd0, 0xfc,
	0x15, 0xd9, 0xc5, 0x2b, 0x80, 0xe3, 0x4e, 0xa4, 0x75, 0xff, 0x52, 0x60, 0x4b, 0xee, 0xcd, 0x67,
	0x44, 0xf7, 0xd8, 0x39, 0xd1, 0x59, 0x9a, 0xa4, 0x7c, 0xb7, 0x27, 0xf8, 0x37, 0x60, 0xcd, 0x25,
	0xb6, 0x61, 0xda, 0x43, 0x8d, 0x67, 0x08, 0xad, 0xad, 0xcc, 0x39, 0x2a, 0x4a, 0x3e, 0x84, 0x0f,
	0x28, 0xfa, 0x18, 0xaa, 0xba, 0xeb, 0x7a, 0xce, 0x6b, 0x73, 0xa4, 0x33, 0xa2, 0x51, 0xf3, 0x0d,
	0xa9, 0x81, 0xc8, 0xc0, 0x4a, 0x64, 0xbe, 0x67, 0xbe, 0x21, 0xf8, 0x12, 0xa0, 0x71, 0xa9, 0xdb,
	0x43, 0xc2, 0x57, 0xa2, 0x5d, 0xc8, 0x72, 0x19, 0xbe, 0xad, 0x71, 0x11, 0x82, 0x82, 0x3e, 0x87,
	0xe2, 0x40, 0xe0, 0x35, 0xf1, 0x18, 0xcb, 0x88, 0xc7, 0xd8, 0xf6, 0x5e, 0xf0, 0xa8, 0xe4, 0xfb,
	0x4a, 0xf2, 0x13, 0xaf, 0x31, 0x18, 0x84, 0xbf, 0xf1, 0x3e, 0x94, 0xfb, 0x9e, 0x6e, 0xd3, 0x0b,
	0xe2, 0x49, 0xb7, 0xdf, 0x2c, 0x0d, 0xff, 0x2d, 0x03, 0xdb, 0x33, 0x81, 0x49, 0x93, 0x7b, 0x53,
	0xf5, 0x85, 0xe4, 0xcc, 0x9c, 0x92, 0x6f, 0xea, 0x8e, 0x40, 0x7d, 0xe1, 0x9a, 0x23, 0xa8, 0x30,
	0x5f, 0x7d, 0x2d, 0x16, 0xb5, 0xb8, 0xdc, 0xb8, 0x89, 0x6a, 0x99, 0xc5, 0x4d, 0x8e, 0x5d, 0x8e,
	0xd9, 0xf8, 0xe5, 0x88, 0x3e, 0x83, 0x92, 0x4f, 0x24, 0xae, 0x33, 0xb8, 0x14, 0x7b, 0x9a, 0x67,
	0x6f, 0x2c, 0x7b, 0x9a, 0x9c, 0xa4, 0x16, 0xbd, 0xe9, 0x00, 0x3d, 0x81, 0x22, 0xd3, 0xbd, 0x21,
	0x61, 0xd2, 0xa8, 0xdc, 0x1c, 0x77, 0x82, 0x04, 0xf0, 0xdf, 0x78, 0x04, 0x95, 0x03, 0xfa, 0xb2,
	0xe7, 0x5a, 0xe6, 0xff, 0x22, 0xcb, 0xf1, 0xcf, 0x15, 0xa8, 0x4e, 0xe5, 0xa5, 0x7b, 0x3c, 0xad,
	0xd9, 0xe4, 0x95, 0x96, 0xac, 0x2e, 0x8a, 0x36, 0x79, 0xa5, 0x06, 0x3e, 0xdc, 0x85, 0x12, 0xc7,
	0x88, 0xc3, 0xd5, 0x34, 0xe4, 0xd9, 0x9a, 0x55, 0xc1, 0x26, 0xaf, 0xb8, 0xed, 0x2d, 0x83, 0xe2,
	0x5f, 0x29, 0x80, 0x54, 0xe2, 0x3a, 0x1e, 0x4b, 0xed, 0x02, 0x0c, 0x59, 0x8b, 0x5c, 0xb0, 0x05,
	0x0e, 0x10, 0x34, 0xf4, 0x00, 0x56, 0x3c, 0x73, 0x78, 0xc9, 0xfc, 0x6c, 0x49, 0x82, 0x24, 0x11,
	0x7f, 0x0f, 0x36, 0x62, 0x3a, 0xa5, 0xb9, 0x97, 0x3a, 0xb0, 0x2a, 0xb8, 0xb4, 0x8e, 0x66, 0x3d,
	0xa6, 0xdc, 0xec, 0xb1, 0xcc, 0x8c, 0xc7, 0x7e, 0x04, 0xa5, 0xbe, 0x39, 0x22, 0x2d, 0x9b, 0x11,
	0xef, 0x4a, 0xb7, 0xf8, 0xf5, 0x23, 0x2b, 0xaf, 0x69, 0x4f, 0x41, 0xf2, 0x2d, 0x8b, 0xe9, 0x69,
	0x1f, 0xe4, 0x3e, 0xac, 0xf1, 0x7a, 0x6b, 0x0a, 0x93, 0x01, 0x2b, 0x11, 0xdb, 0x08, 0x41, 0xf8,
	0x29, 0x80, 0x4a, 0x06, 0x8e, 0x67, 0x74, 0x75, 0xd3, 0x43, 0x55, 0x58, 0xe6, 0xe5, 0x99, 0xbc,
	0x48, 0xf9, 0x4f, 0x5e, 0xca, 0x5d, 0xe9, 0xd6, 0x98, 0xf8, 0x8b, 0xe5, 0x00, 0xff, 0x72, 0x05,
	0x60, 0xfa, 0x38, 0x8b, 0x3d, 0x27, 0x95, 0xd8, 0x73, 0x12, 0xd5, 0x21, 0x3f, 0xd0, 0x5d, 0x7d,
	0xc0, 0x6f, 0x49, 0xff, 0x1a, 0x0e, 0xc6, 0xe8, 0x0e, 0x14, 0xf4, 0x2b, 0xdd, 0xb4, 0xf4, 0x73,
	0x8b, 0x88, 0x00, 0x65, 0xd5, 0xe9, 0x04, 0xba, 0x17, 0xee, 0x47, 0xd9, 0x52, 0xc9, 0x8a, 0x96,
	0x8a, 0xbf, 0xf5, 0x1a, 0xa2, 0xb1, 0xf2, 0x35, 0x40, 0xd4, 0x3f, 0x9c, 0xa9, 0xad, 0xbb, 0x3e,
	0x70, 0x45, 0x00, 0xab, 0x3e, 0xa5, 0x67, 0xeb, 0xae, 0x44, 0x7f, 0x02, 0x9b, 0x1e, 0x19, 0x10,
	0xf3, 0x2a, 0x81, 0xcf, 0x09, 0x3c, 0x0a, 0x69, 0xd3, 0x15, 0x77, 0x01, 0xa6, 0xae, 0xae, 0xad,
	0x0a, 0x5c, 0x21, 0xf4, 0x32, 0xda, 0x83, 0x0d, 0xdd, 0x75, 0xad, 0x49, 0x82, 0x5f, 0x5e, 0xe0,
	0xd6, 0x03, 0xd2, 0x94, 0xdd, 0x36, 0xac, 0x9a, 0x54, 0x3b, 0x1f, 0xd3, 0x49, 0xad, 0x20, 0x9e,
	0x6a, 0x39, 0x93, 0x1e, 0x8e, 0xe9, 0x84, 0x9f, 0x4b, 0x63, 0x4a, 0x8c, 0xe8, 0x55, 0x91, 0xe7,
	0x13, 0xfc, 0x8e, 0x40, 0xdf, 0x84, 0xbc, 0xe9, 0xc7, 0xbe, 0x56, 0x11, 0x79, 0xf8, 0xfe, 0x4c,
	0xf3, 0x28, 0x48, 0x0e, 0x35, 0x84, 0xa2, 0xcf, 0x00, 0x06, 0xee, 0x58, 0x1b, 0x53, 0x7d, 0x48,
	0x68, 0xad, 0x2a, 0x6e, 0xad, 0xed, 0x44, 0x02, 0x07, 0x71, 0x57, 0x0b, 0x03, 0x77, 0x7c, 0x26,
	0x90, 0xe8, 0xdb, 0xb0, 0xe6, 0x11, 0xdd, 0xd0, 0x4c, 0x47, 0xf3, 0x74, 0x46, 0x68, 0x6d, 0xfd,
	0xfa, 0xa5, 0x45, 0x8e, 0x6e, 0x39, 0x2a, 0xc7, 0xa2, 0xef, 0x40, 0xf9, 0x95, 0x67, 0x32, 0x32,
	0x5d, 0x8d, 0xae, 0x5f, 0x5d, 0x12, 0xf0, 0x60, 0xf9, 0xb7, 0xa0, 0xe4, 0xb8, 0x9a, 0xa5, 0x33,
	0x62, 0x0f, 0x4c, 0x42, 0x6b, 0x1b, 0x37, 0x88, 0x76, 0xdc, 0x93, 0x00, 0x8b, 0xdf, 0xc0, 0x7b,
	0x22, 0x23, 0xdf, 0x49, 0x0d, 0x11, 0x76, 0x25, 0x32, 0xb7, 0xea, 0x4a, 0x9c, 0xc2, 0x56, 0x52,
	0x76, 0x9a, 0x23, 0xe4, 0x8f, 0x0a, 0x6c, 0xf6, 0x06, 0x3a, 0xe3, 0x05, 0x74, 0xea, 0xa7, 0xf3,
	0x75, 0x0f, 0xc2, 0xc8, 0x2d, 0xb2, 0x7c, 0xcb, 0x5a, 0x29, 0xbb, 0xb8, 0x56, 0xc2, 0x27, 0xf0,
	0x5e, 0x42, 0xed, 0x94, 0x8d, 0xc4, 0x63, 0xc2, 0x8e, 0x1b, 0x3d, 0xfd, 0x82, 0x74, 0x1d, 0xd3,
	0x4e, 0x13, 0x50, 0x6c, 0xc1, 0x56, 0x92, 0x59, 0x9a, 0xbb, 0x90, 0x1f, 0x0c, 0xfa, 0x05, 0xd1,
	0x5c, 0xce, 0xca, 0xf7, 0x6a, 0x81, 0x06, 0xbc, 0xf1, 0x08, 0x6a, 0x67, 0xae, 0xa1, 0x33, 0xf2,
	0x6e, 0xb4, 0xbf, 0x49, 0xdc, 0x15, 0xbc, 0x3f, 0x47, 0x5c, 0x1a, 0xfb, 0x1e, 0x40, 0x99, 0xdf,
	0x4a, 0x33, 0x42, 0xf9, 0x5d, 0x15, 0x8a, 0xc0, 0x44, 0xbc, 0x4a, 0x3a, 0x2e, 0xf1, 0x74, 0xe6,
	0x78, 0xff, 0xb5, 0xae, 0xc5, 0x9f, 0x65, 0xfb, 0x6c, 0x2a, 0x27, 0x8d, 0x65, 0xd7, 0x6e, 0x07,
	0x04, 0x59, 0x83, 0xd0, 0x81, 0xd8, 0x0c, 0x25, 0x55, 0xfc, 0xe6, 0x52, 0xf8, 0x26, 0x1f, 0x53,
	0x91, 0xfa, 0xe5, 0x84, 0x94, 0x40, 0xa9, 0x9e, 0x80, 0xa8, 0x3e, 0x94, 0x33, 0x7a, 0x69, 0xda,
	0x86, 0xb8, 0x8a, 0x4a, 0xaa, 0xf8, 0xfd, 0xf8, 0x37, 0x0a, 0x14, 0xc2, 0x2f, 0x25, 0x28, 0x07,
	0x99, 0xce, 0xf3, 0xea, 0x12, 0x2a, 0xc2, 0xea, 0x59, 0xfb, 0x79, 0xbb, 0xf3, 0xfd, 0x76, 0x55,
	0x41, 0x9b, 0x50, 0x6d, 0x77, 0xfa, 0xda, 0x61, 0xa7, 0xd3, 0xef, 0xf5, 0xd5, 0x83, 0x6e, 0xb7,
	0x79, 0x54, 0xcd, 0xa0, 0x0d, 0xa8, 0xf4, 0xfa, 0x1d, 0xb5, 0xa9, 0xf5, 0x3b, 0xa7, 0x87, 0xbd,
	0x7e, 0xa7, 0xdd, 0xac, 0x2e, 0xa3, 0x1a, 0x6c, 0x1e, 0x9c, 0xa8, 0xcd, 0x83, 0xa3, 0x2f, 0xe3,
	0xf0, 0x2c, 0xa7, 0xb4, 0xda, 0x8d, 0xce, 0x69, 0xf7, 0xa0, 0xdf, 0x3a, 0x3c, 0x69, 0x6a, 0x2f,
	0x9a, 0x6a, 0xaf, 0xd5, 0x69, 0x57, 0x57, 0x38, 0x7b, 0xb5, 0x79, 0xdc, 0xea, 0xb4, 0x35, 0x2e,
	0xe5, 0x8b, 0xce, 0x59, 0xfb, 0xa8, 0x9a, 0x7b, 0xdc, 0x85, 0x72, 0xdc, 0x0a, 0xae, 0x53, 0xef,
	0xac, 0xd1, 0x68, 0xf6, 0x7a, 0x52, 0xc1, 0x7e, 0xeb, 0xb4, 0xd9, 0x39, 0xeb, 0x57, 0x15, 0x04,
	0x90, 0x6b, 0x1c, 0xb4, 0x1b, 0xcd, 0x93, 0x6a, 0x86, 0x13, 0xd4, 0x66, 0xf7, 0xe4, 0xa0, 0xc1,
	0xd5, 0xe1, 0x83, 0xb3, 0x76, 0xbb, 0xd5, 0x3e, 0xae, 0x66, 0xf7, 0x7f, 0x56, 0x86, 0x42, 0x2f,
	0x70, 0x12, 0xea, 0x00, 0x4c, 0xdf, 0xae, 0x68, 0x27, 0xe6, 0xbe, 0x99, 0xe7, 0x71, 0xfd, 0xc3,
	0x85, 0x74, 0x19, 0x4e, 0xbc, 0x84, 0xbe, 0x0b, 0xcb, 0x7d, 0xea, 0xa0, 0xf8, 0xa1, 0x3c, 0xfd,
	0xac, 0x54, 0xaf, 0xcd, 0x12, 0x82, 0xb5, 0x8f, 0x94, 0x4f, 0x14, 0x74, 0x02, 0x85, 0xf0, 0x93,
	0x02, 0xba, 0x1b, 0x03, 0x27, 0x3f, 0xb8, 0xd4, 0x77, 0x16, 0x91, 0x43, 0x6d, 0x7e, 0x08, 0xe5,
	0xf8, 0x27, 0x0a, 0x84, 0x63, 0x6b, 0xe6, 0x7e, 0x0c, 0xa9, 0xdf, 0xbf, 0x16, 0x13, 0x32, 0xff,
	0x02, 0x56, 0xfd, 0xcf, 0x08, 0x28, 0x9e, 0x77, 0xf1, 0x4f, 0x14, 0xf5, 0x3b, 0xf3, 0x89, 0x21,
	0x9f, 0x16, 0xe4, 0x83, 0x9e, 0x3e, 0xba, 0x93, 0xf4, 0x70, 0xb4, 0x9b, 0x5e, 0xbf, 0xbb, 0x80,
	0x1a, 0x65, 0x15, 0x74, 0xc5, 0x13, 0xac, 0x12, 0x8d, 0xf9, 0x04, 0xab, 0x64, 0x2b, 0x1d, 0x2f,
	0xa1, 0x33, 0x28, 0x45, 0x3b, 0xd2, 0x68, 0x37, 0x29, 0x3b, 0xd9, 0x31, 0xaf, 0xdf, 0xbb, 0x06,
	0x11, 0x8d, 0x48, 0xfc, 0x36, 0x4e, 0x44, 0x64, 0x6e, 0x99, 0x90, 0x88, 0xc8, 0xfc, 0xeb, 0x1c,
	0x2f, 0xa1, 0x73, 0xa8, 0x24, 0x9e, 0xc4, 0xe8, 0x7e, 0xe2, 0xdc, 0x99, 0xd7, 0xc9, 0xa8, 0x3f,
	0xb8, 0x1e, 0x94, 0x4c, 0xd0, 0xb0, 0x1f, 0x8c, 0x66, 0x02, 0x12, 0x2b, 0x09, 0xea, 0x3b, 0x8b,
	0xc8, 0xa1, 0xc6, 0x5d, 0x58, 0x3b, 0x26, 0xac, 0xeb, 0x91, 0xab, 0x77, 0xc5, 0xb1, 0x2f, 0x38,
	0x4e, 0xfb, 0xd5, 0xe8, 0xde, 0xfc, 0x25, 0x91, 0x5e, 0xf6, 0x2d, 0xb8, 0xaa, 0x50, 0x8c, 0x34,
	0x81, 0x51, 0xfc, 0x20, 0x98, 0xed, 0x5a, 0xd7, 0x77, 0x17, 0x03, 0xa2, 0xc9, 0x1a, 0x3c, 0x7e,
	0x13, 0xc9, 0x9a, 0x78, 0x83, 0x27, 0x92, 0x35, 0xf9, 0x62, 0xc6, 0x4b, 0x48, 0x17, 0x9f, 0x32,
	0x62, 0x0d, 0x4c, 0xf4, 0x20, 0x69, 0xd4, 0xbc, 0xce, 0x6a, 0xfd, 0xa3, 0x1b, 0x50, 0x51, 0x11,
	0xc9, 0x1e, 0x69, 0x42, 0xc4, 0x82, 0xe6, 0x6d, 0x42, 0xc4, 0xa2, 0x46, 0x2b, 0x5e, 0x42, 0x3f,
	0x80, 0xb5, 0x58, 0x89, 0x96, 0x08, 0xdd, 0xbc, 0xaa, 0xb3, 0x8e, 0xaf, 0x83, 0x44, 0x77, 0x5d,
	0xbc, 0xc2, 0x4a, 0xec, 0xba, 0xb9, 0xb5, 0x5c, 0x62, 0xd7, 0xcd, 0x2f, 0xd1, 0xf0, 0x12, 0x32,
	0x60, 0x7d, 0xa6, 0xc2, 0x41, 0x71, 0xa3, 0x17, 0x15, 0x5c, 0xf5, 0x87, 0x37, 0xc1, 0xa2, 0x19,
	0x18, 0xa9, 0x33, 0xd0, 0xcc, 0x55, 0x94, 0xa8, 0x74, 0xea, 0xbb, 0x8b, 0x01, 0x01, 0xcf, 0xc3,
	0xea, 0x5f, 0xde, 0xee, 0x28, 0x7f, 0x7d, 0xbb, 0xa3, 0xfc, 0xe3, 0xed, 0x8e, 0xf2, 0xdb, 0x7f,
	0xee, 0x2c, 0x9d, 0xe7, 0xc4, 0x9f, 0x3c, 0x3e, 0xfd, 0x77, 0x00, 0x00, 0x00, 0xff, 0xff, 0x40,
	0x5d, 0x19, 0x5a, 0x39, 0x22, 0x00, 0x00,
}
