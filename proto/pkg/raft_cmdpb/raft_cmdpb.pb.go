// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: raft_cmdpb.proto

package raft_cmdpb

import (
	"fmt"
	"io"
	"math"

	proto "github.com/golang/protobuf/proto"

	eraftpb "github.com/pingcap-incubator/tinykv/proto/pkg/eraftpb"
	errorpb "github.com/pingcap-incubator/tinykv/proto/pkg/errorpb"

	metapb "github.com/pingcap-incubator/tinykv/proto/pkg/metapb"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CmdType int32

const (
	CmdType_Invalid CmdType = 0
	CmdType_Get     CmdType = 1
	CmdType_Put     CmdType = 3
	CmdType_Delete  CmdType = 4
	CmdType_Snap    CmdType = 5
)

var CmdType_name = map[int32]string{
	0: "Invalid",
	1: "Get",
	3: "Put",
	4: "Delete",
	5: "Snap",
}
var CmdType_value = map[string]int32{
	"Invalid": 0,
	"Get":     1,
	"Put":     3,
	"Delete":  4,
	"Snap":    5,
}

func (x CmdType) String() string {
	return proto.EnumName(CmdType_name, int32(x))
}
func (CmdType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{0}
}

type AdminCmdType int32

const (
	AdminCmdType_InvalidAdmin   AdminCmdType = 0
	AdminCmdType_ChangePeer     AdminCmdType = 1
	AdminCmdType_CompactLog     AdminCmdType = 3
	AdminCmdType_TransferLeader AdminCmdType = 4
	AdminCmdType_Split          AdminCmdType = 10
)

var AdminCmdType_name = map[int32]string{
	0:  "InvalidAdmin",
	1:  "ChangePeer",
	3:  "CompactLog",
	4:  "TransferLeader",
	10: "Split",
}
var AdminCmdType_value = map[string]int32{
	"InvalidAdmin":   0,
	"ChangePeer":     1,
	"CompactLog":     3,
	"TransferLeader": 4,
	"Split":          10,
}

func (x AdminCmdType) String() string {
	return proto.EnumName(AdminCmdType_name, int32(x))
}
func (AdminCmdType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{1}
}

type GetRequest struct {
	Cf                   string   `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	Key                  []byte   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRequest) Reset()         { *m = GetRequest{} }
func (m *GetRequest) String() string { return proto.CompactTextString(m) }
func (*GetRequest) ProtoMessage()    {}
func (*GetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{0}
}
func (m *GetRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRequest.Merge(dst, src)
}
func (m *GetRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRequest proto.InternalMessageInfo

func (m *GetRequest) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *GetRequest) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

type GetResponse struct {
	Value                []byte   `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetResponse) Reset()         { *m = GetResponse{} }
func (m *GetResponse) String() string { return proto.CompactTextString(m) }
func (*GetResponse) ProtoMessage()    {}
func (*GetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{1}
}
func (m *GetResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *GetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResponse.Merge(dst, src)
}
func (m *GetResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetResponse proto.InternalMessageInfo

func (m *GetResponse) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type PutRequest struct {
	Cf                   string   `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	Key                  []byte   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Value                []byte   `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PutRequest) Reset()         { *m = PutRequest{} }
func (m *PutRequest) String() string { return proto.CompactTextString(m) }
func (*PutRequest) ProtoMessage()    {}
func (*PutRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{2}
}
func (m *PutRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *PutRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutRequest.Merge(dst, src)
}
func (m *PutRequest) XXX_Size() int {
	return m.Size()
}
func (m *PutRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PutRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PutRequest proto.InternalMessageInfo

func (m *PutRequest) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *PutRequest) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *PutRequest) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type PutResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PutResponse) Reset()         { *m = PutResponse{} }
func (m *PutResponse) String() string { return proto.CompactTextString(m) }
func (*PutResponse) ProtoMessage()    {}
func (*PutResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{3}
}
func (m *PutResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *PutResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutResponse.Merge(dst, src)
}
func (m *PutResponse) XXX_Size() int {
	return m.Size()
}
func (m *PutResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PutResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PutResponse proto.InternalMessageInfo

type DeleteRequest struct {
	Cf                   string   `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	Key                  []byte   `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRequest) Reset()         { *m = DeleteRequest{} }
func (m *DeleteRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteRequest) ProtoMessage()    {}
func (*DeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{4}
}
func (m *DeleteRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *DeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRequest.Merge(dst, src)
}
func (m *DeleteRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRequest proto.InternalMessageInfo

func (m *DeleteRequest) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *DeleteRequest) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

type DeleteResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteResponse) Reset()         { *m = DeleteResponse{} }
func (m *DeleteResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteResponse) ProtoMessage()    {}
func (*DeleteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{5}
}
func (m *DeleteResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *DeleteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteResponse.Merge(dst, src)
}
func (m *DeleteResponse) XXX_Size() int {
	return m.Size()
}
func (m *DeleteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteResponse proto.InternalMessageInfo

type SnapRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SnapRequest) Reset()         { *m = SnapRequest{} }
func (m *SnapRequest) String() string { return proto.CompactTextString(m) }
func (*SnapRequest) ProtoMessage()    {}
func (*SnapRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{6}
}
func (m *SnapRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *SnapRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapRequest.Merge(dst, src)
}
func (m *SnapRequest) XXX_Size() int {
	return m.Size()
}
func (m *SnapRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SnapRequest proto.InternalMessageInfo

type SnapResponse struct {
	Region               *metapb.Region `protobuf:"bytes,1,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SnapResponse) Reset()         { *m = SnapResponse{} }
func (m *SnapResponse) String() string { return proto.CompactTextString(m) }
func (*SnapResponse) ProtoMessage()    {}
func (*SnapResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{7}
}
func (m *SnapResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *SnapResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapResponse.Merge(dst, src)
}
func (m *SnapResponse) XXX_Size() int {
	return m.Size()
}
func (m *SnapResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SnapResponse proto.InternalMessageInfo

func (m *SnapResponse) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

type Request struct {
	CmdType              CmdType        `protobuf:"varint,1,opt,name=cmd_type,json=cmdType,proto3,enum=raft_cmdpb.CmdType" json:"cmd_type,omitempty"`
	Get                  *GetRequest    `protobuf:"bytes,2,opt,name=get" json:"get,omitempty"`
	Put                  *PutRequest    `protobuf:"bytes,4,opt,name=put" json:"put,omitempty"`
	Delete               *DeleteRequest `protobuf:"bytes,5,opt,name=delete" json:"delete,omitempty"`
	Snap                 *SnapRequest   `protobuf:"bytes,6,opt,name=snap" json:"snap,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *Request) Reset()         { *m = Request{} }
func (m *Request) String() string { return proto.CompactTextString(m) }
func (*Request) ProtoMessage()    {}
func (*Request) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{8}
}
func (m *Request) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Request) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Request.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *Request) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Request.Merge(dst, src)
}
func (m *Request) XXX_Size() int {
	return m.Size()
}
func (m *Request) XXX_DiscardUnknown() {
	xxx_messageInfo_Request.DiscardUnknown(m)
}

var xxx_messageInfo_Request proto.InternalMessageInfo

func (m *Request) GetCmdType() CmdType {
	if m != nil {
		return m.CmdType
	}
	return CmdType_Invalid
}

func (m *Request) GetGet() *GetRequest {
	if m != nil {
		return m.Get
	}
	return nil
}

func (m *Request) GetPut() *PutRequest {
	if m != nil {
		return m.Put
	}
	return nil
}

func (m *Request) GetDelete() *DeleteRequest {
	if m != nil {
		return m.Delete
	}
	return nil
}

func (m *Request) GetSnap() *SnapRequest {
	if m != nil {
		return m.Snap
	}
	return nil
}

type Response struct {
	CmdType              CmdType         `protobuf:"varint,1,opt,name=cmd_type,json=cmdType,proto3,enum=raft_cmdpb.CmdType" json:"cmd_type,omitempty"`
	Get                  *GetResponse    `protobuf:"bytes,2,opt,name=get" json:"get,omitempty"`
	Put                  *PutResponse    `protobuf:"bytes,4,opt,name=put" json:"put,omitempty"`
	Delete               *DeleteResponse `protobuf:"bytes,5,opt,name=delete" json:"delete,omitempty"`
	Snap                 *SnapResponse   `protobuf:"bytes,6,opt,name=snap" json:"snap,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{9}
}
func (m *Response) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Response.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(dst, src)
}
func (m *Response) XXX_Size() int {
	return m.Size()
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetCmdType() CmdType {
	if m != nil {
		return m.CmdType
	}
	return CmdType_Invalid
}

func (m *Response) GetGet() *GetResponse {
	if m != nil {
		return m.Get
	}
	return nil
}

func (m *Response) GetPut() *PutResponse {
	if m != nil {
		return m.Put
	}
	return nil
}

func (m *Response) GetDelete() *DeleteResponse {
	if m != nil {
		return m.Delete
	}
	return nil
}

func (m *Response) GetSnap() *SnapResponse {
	if m != nil {
		return m.Snap
	}
	return nil
}

type ChangePeerRequest struct {
	// This can be only called in internal Raftstore now.
	ChangeType           eraftpb.ConfChangeType `protobuf:"varint,1,opt,name=change_type,json=changeType,proto3,enum=eraftpb.ConfChangeType" json:"change_type,omitempty"`
	Peer                 *metapb.Peer           `protobuf:"bytes,2,opt,name=peer" json:"peer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ChangePeerRequest) Reset()         { *m = ChangePeerRequest{} }
func (m *ChangePeerRequest) String() string { return proto.CompactTextString(m) }
func (*ChangePeerRequest) ProtoMessage()    {}
func (*ChangePeerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{10}
}
func (m *ChangePeerRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangePeerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangePeerRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ChangePeerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangePeerRequest.Merge(dst, src)
}
func (m *ChangePeerRequest) XXX_Size() int {
	return m.Size()
}
func (m *ChangePeerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangePeerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChangePeerRequest proto.InternalMessageInfo

func (m *ChangePeerRequest) GetChangeType() eraftpb.ConfChangeType {
	if m != nil {
		return m.ChangeType
	}
	return eraftpb.ConfChangeType_AddNode
}

func (m *ChangePeerRequest) GetPeer() *metapb.Peer {
	if m != nil {
		return m.Peer
	}
	return nil
}

type ChangePeerResponse struct {
	Region               *metapb.Region `protobuf:"bytes,1,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ChangePeerResponse) Reset()         { *m = ChangePeerResponse{} }
func (m *ChangePeerResponse) String() string { return proto.CompactTextString(m) }
func (*ChangePeerResponse) ProtoMessage()    {}
func (*ChangePeerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{11}
}
func (m *ChangePeerResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangePeerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangePeerResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *ChangePeerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangePeerResponse.Merge(dst, src)
}
func (m *ChangePeerResponse) XXX_Size() int {
	return m.Size()
}
func (m *ChangePeerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangePeerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChangePeerResponse proto.InternalMessageInfo

func (m *ChangePeerResponse) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

type SplitRequest struct {
	// This can be only called in internal Raftstore now.
	// The split_key has to exist in the splitting region.
	SplitKey []byte `protobuf:"bytes,1,opt,name=split_key,json=splitKey,proto3" json:"split_key,omitempty"`
	// We split the region into two. The first uses the origin
	// parent region id, and the second uses the new_region_id.
	// We must guarantee that the new_region_id is global unique.
	NewRegionId uint64 `protobuf:"varint,2,opt,name=new_region_id,json=newRegionId,proto3" json:"new_region_id,omitempty"`
	// The peer ids for the new split region.
	NewPeerIds           []uint64 `protobuf:"varint,3,rep,packed,name=new_peer_ids,json=newPeerIds" json:"new_peer_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SplitRequest) Reset()         { *m = SplitRequest{} }
func (m *SplitRequest) String() string { return proto.CompactTextString(m) }
func (*SplitRequest) ProtoMessage()    {}
func (*SplitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{12}
}
func (m *SplitRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SplitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SplitRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *SplitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SplitRequest.Merge(dst, src)
}
func (m *SplitRequest) XXX_Size() int {
	return m.Size()
}
func (m *SplitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SplitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SplitRequest proto.InternalMessageInfo

func (m *SplitRequest) GetSplitKey() []byte {
	if m != nil {
		return m.SplitKey
	}
	return nil
}

func (m *SplitRequest) GetNewRegionId() uint64 {
	if m != nil {
		return m.NewRegionId
	}
	return 0
}

func (m *SplitRequest) GetNewPeerIds() []uint64 {
	if m != nil {
		return m.NewPeerIds
	}
	return nil
}

type SplitResponse struct {
	// SplitResponse contains the region where specific keys have split into.
	Regions              []*metapb.Region `protobuf:"bytes,1,rep,name=regions" json:"regions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SplitResponse) Reset()         { *m = SplitResponse{} }
func (m *SplitResponse) String() string { return proto.CompactTextString(m) }
func (*SplitResponse) ProtoMessage()    {}
func (*SplitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{13}
}
func (m *SplitResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SplitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SplitResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *SplitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SplitResponse.Merge(dst, src)
}
func (m *SplitResponse) XXX_Size() int {
	return m.Size()
}
func (m *SplitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SplitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SplitResponse proto.InternalMessageInfo

func (m *SplitResponse) GetRegions() []*metapb.Region {
	if m != nil {
		return m.Regions
	}
	return nil
}

type CompactLogRequest struct {
	CompactIndex         uint64   `protobuf:"varint,1,opt,name=compact_index,json=compactIndex,proto3" json:"compact_index,omitempty"`
	CompactTerm          uint64   `protobuf:"varint,2,opt,name=compact_term,json=compactTerm,proto3" json:"compact_term,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompactLogRequest) Reset()         { *m = CompactLogRequest{} }
func (m *CompactLogRequest) String() string { return proto.CompactTextString(m) }
func (*CompactLogRequest) ProtoMessage()    {}
func (*CompactLogRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{14}
}
func (m *CompactLogRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CompactLogRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CompactLogRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *CompactLogRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompactLogRequest.Merge(dst, src)
}
func (m *CompactLogRequest) XXX_Size() int {
	return m.Size()
}
func (m *CompactLogRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CompactLogRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CompactLogRequest proto.InternalMessageInfo

func (m *CompactLogRequest) GetCompactIndex() uint64 {
	if m != nil {
		return m.CompactIndex
	}
	return 0
}

func (m *CompactLogRequest) GetCompactTerm() uint64 {
	if m != nil {
		return m.CompactTerm
	}
	return 0
}

type CompactLogResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompactLogResponse) Reset()         { *m = CompactLogResponse{} }
func (m *CompactLogResponse) String() string { return proto.CompactTextString(m) }
func (*CompactLogResponse) ProtoMessage()    {}
func (*CompactLogResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{15}
}
func (m *CompactLogResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CompactLogResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CompactLogResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *CompactLogResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompactLogResponse.Merge(dst, src)
}
func (m *CompactLogResponse) XXX_Size() int {
	return m.Size()
}
func (m *CompactLogResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CompactLogResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CompactLogResponse proto.InternalMessageInfo

type TransferLeaderRequest struct {
	Peer                 *metapb.Peer `protobuf:"bytes,1,opt,name=peer" json:"peer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TransferLeaderRequest) Reset()         { *m = TransferLeaderRequest{} }
func (m *TransferLeaderRequest) String() string { return proto.CompactTextString(m) }
func (*TransferLeaderRequest) ProtoMessage()    {}
func (*TransferLeaderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{16}
}
func (m *TransferLeaderRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TransferLeaderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TransferLeaderRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *TransferLeaderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferLeaderRequest.Merge(dst, src)
}
func (m *TransferLeaderRequest) XXX_Size() int {
	return m.Size()
}
func (m *TransferLeaderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferLeaderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TransferLeaderRequest proto.InternalMessageInfo

func (m *TransferLeaderRequest) GetPeer() *metapb.Peer {
	if m != nil {
		return m.Peer
	}
	return nil
}

type TransferLeaderResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransferLeaderResponse) Reset()         { *m = TransferLeaderResponse{} }
func (m *TransferLeaderResponse) String() string { return proto.CompactTextString(m) }
func (*TransferLeaderResponse) ProtoMessage()    {}
func (*TransferLeaderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{17}
}
func (m *TransferLeaderResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TransferLeaderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TransferLeaderResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *TransferLeaderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferLeaderResponse.Merge(dst, src)
}
func (m *TransferLeaderResponse) XXX_Size() int {
	return m.Size()
}
func (m *TransferLeaderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferLeaderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TransferLeaderResponse proto.InternalMessageInfo

type AdminRequest struct {
	CmdType              AdminCmdType           `protobuf:"varint,1,opt,name=cmd_type,json=cmdType,proto3,enum=raft_cmdpb.AdminCmdType" json:"cmd_type,omitempty"`
	ChangePeer           *ChangePeerRequest     `protobuf:"bytes,2,opt,name=change_peer,json=changePeer" json:"change_peer,omitempty"`
	CompactLog           *CompactLogRequest     `protobuf:"bytes,4,opt,name=compact_log,json=compactLog" json:"compact_log,omitempty"`
	TransferLeader       *TransferLeaderRequest `protobuf:"bytes,5,opt,name=transfer_leader,json=transferLeader" json:"transfer_leader,omitempty"`
	Split                *SplitRequest          `protobuf:"bytes,10,opt,name=split" json:"split,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AdminRequest) Reset()         { *m = AdminRequest{} }
func (m *AdminRequest) String() string { return proto.CompactTextString(m) }
func (*AdminRequest) ProtoMessage()    {}
func (*AdminRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{18}
}
func (m *AdminRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AdminRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AdminRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *AdminRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminRequest.Merge(dst, src)
}
func (m *AdminRequest) XXX_Size() int {
	return m.Size()
}
func (m *AdminRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AdminRequest proto.InternalMessageInfo

func (m *AdminRequest) GetCmdType() AdminCmdType {
	if m != nil {
		return m.CmdType
	}
	return AdminCmdType_InvalidAdmin
}

func (m *AdminRequest) GetChangePeer() *ChangePeerRequest {
	if m != nil {
		return m.ChangePeer
	}
	return nil
}

func (m *AdminRequest) GetCompactLog() *CompactLogRequest {
	if m != nil {
		return m.CompactLog
	}
	return nil
}

func (m *AdminRequest) GetTransferLeader() *TransferLeaderRequest {
	if m != nil {
		return m.TransferLeader
	}
	return nil
}

func (m *AdminRequest) GetSplit() *SplitRequest {
	if m != nil {
		return m.Split
	}
	return nil
}

type AdminResponse struct {
	CmdType              AdminCmdType            `protobuf:"varint,1,opt,name=cmd_type,json=cmdType,proto3,enum=raft_cmdpb.AdminCmdType" json:"cmd_type,omitempty"`
	ChangePeer           *ChangePeerResponse     `protobuf:"bytes,2,opt,name=change_peer,json=changePeer" json:"change_peer,omitempty"`
	CompactLog           *CompactLogResponse     `protobuf:"bytes,4,opt,name=compact_log,json=compactLog" json:"compact_log,omitempty"`
	TransferLeader       *TransferLeaderResponse `protobuf:"bytes,5,opt,name=transfer_leader,json=transferLeader" json:"transfer_leader,omitempty"`
	Split                *SplitResponse          `protobuf:"bytes,10,opt,name=split" json:"split,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AdminResponse) Reset()         { *m = AdminResponse{} }
func (m *AdminResponse) String() string { return proto.CompactTextString(m) }
func (*AdminResponse) ProtoMessage()    {}
func (*AdminResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{19}
}
func (m *AdminResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AdminResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AdminResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *AdminResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminResponse.Merge(dst, src)
}
func (m *AdminResponse) XXX_Size() int {
	return m.Size()
}
func (m *AdminResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AdminResponse proto.InternalMessageInfo

func (m *AdminResponse) GetCmdType() AdminCmdType {
	if m != nil {
		return m.CmdType
	}
	return AdminCmdType_InvalidAdmin
}

func (m *AdminResponse) GetChangePeer() *ChangePeerResponse {
	if m != nil {
		return m.ChangePeer
	}
	return nil
}

func (m *AdminResponse) GetCompactLog() *CompactLogResponse {
	if m != nil {
		return m.CompactLog
	}
	return nil
}

func (m *AdminResponse) GetTransferLeader() *TransferLeaderResponse {
	if m != nil {
		return m.TransferLeader
	}
	return nil
}

func (m *AdminResponse) GetSplit() *SplitResponse {
	if m != nil {
		return m.Split
	}
	return nil
}

type RaftRequestHeader struct {
	RegionId             uint64              `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	Peer                 *metapb.Peer        `protobuf:"bytes,2,opt,name=peer" json:"peer,omitempty"`
	RegionEpoch          *metapb.RegionEpoch `protobuf:"bytes,4,opt,name=region_epoch,json=regionEpoch" json:"region_epoch,omitempty"`
	Term                 uint64              `protobuf:"varint,5,opt,name=term,proto3" json:"term,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *RaftRequestHeader) Reset()         { *m = RaftRequestHeader{} }
func (m *RaftRequestHeader) String() string { return proto.CompactTextString(m) }
func (*RaftRequestHeader) ProtoMessage()    {}
func (*RaftRequestHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{20}
}
func (m *RaftRequestHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftRequestHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftRequestHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RaftRequestHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftRequestHeader.Merge(dst, src)
}
func (m *RaftRequestHeader) XXX_Size() int {
	return m.Size()
}
func (m *RaftRequestHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftRequestHeader.DiscardUnknown(m)
}

var xxx_messageInfo_RaftRequestHeader proto.InternalMessageInfo

func (m *RaftRequestHeader) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RaftRequestHeader) GetPeer() *metapb.Peer {
	if m != nil {
		return m.Peer
	}
	return nil
}

func (m *RaftRequestHeader) GetRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *RaftRequestHeader) GetTerm() uint64 {
	if m != nil {
		return m.Term
	}
	return 0
}

type RaftResponseHeader struct {
	Error                *errorpb.Error `protobuf:"bytes,1,opt,name=error" json:"error,omitempty"`
	Uuid                 []byte         `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	CurrentTerm          uint64         `protobuf:"varint,3,opt,name=current_term,json=currentTerm,proto3" json:"current_term,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RaftResponseHeader) Reset()         { *m = RaftResponseHeader{} }
func (m *RaftResponseHeader) String() string { return proto.CompactTextString(m) }
func (*RaftResponseHeader) ProtoMessage()    {}
func (*RaftResponseHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{21}
}
func (m *RaftResponseHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftResponseHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftResponseHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RaftResponseHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftResponseHeader.Merge(dst, src)
}
func (m *RaftResponseHeader) XXX_Size() int {
	return m.Size()
}
func (m *RaftResponseHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftResponseHeader.DiscardUnknown(m)
}

var xxx_messageInfo_RaftResponseHeader proto.InternalMessageInfo

func (m *RaftResponseHeader) GetError() *errorpb.Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *RaftResponseHeader) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

func (m *RaftResponseHeader) GetCurrentTerm() uint64 {
	if m != nil {
		return m.CurrentTerm
	}
	return 0
}

type RaftCmdRequest struct {
	Header *RaftRequestHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	// We can't enclose normal requests and administrator request
	// at same time.
	Requests             []*Request    `protobuf:"bytes,2,rep,name=requests" json:"requests,omitempty"`
	AdminRequest         *AdminRequest `protobuf:"bytes,3,opt,name=admin_request,json=adminRequest" json:"admin_request,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *RaftCmdRequest) Reset()         { *m = RaftCmdRequest{} }
func (m *RaftCmdRequest) String() string { return proto.CompactTextString(m) }
func (*RaftCmdRequest) ProtoMessage()    {}
func (*RaftCmdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{22}
}
func (m *RaftCmdRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftCmdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftCmdRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RaftCmdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftCmdRequest.Merge(dst, src)
}
func (m *RaftCmdRequest) XXX_Size() int {
	return m.Size()
}
func (m *RaftCmdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftCmdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RaftCmdRequest proto.InternalMessageInfo

func (m *RaftCmdRequest) GetHeader() *RaftRequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *RaftCmdRequest) GetRequests() []*Request {
	if m != nil {
		return m.Requests
	}
	return nil
}

func (m *RaftCmdRequest) GetAdminRequest() *AdminRequest {
	if m != nil {
		return m.AdminRequest
	}
	return nil
}

type RaftCmdResponse struct {
	Header               *RaftResponseHeader `protobuf:"bytes,1,opt,name=header" json:"header,omitempty"`
	Responses            []*Response         `protobuf:"bytes,2,rep,name=responses" json:"responses,omitempty"`
	AdminResponse        *AdminResponse      `protobuf:"bytes,3,opt,name=admin_response,json=adminResponse" json:"admin_response,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *RaftCmdResponse) Reset()         { *m = RaftCmdResponse{} }
func (m *RaftCmdResponse) String() string { return proto.CompactTextString(m) }
func (*RaftCmdResponse) ProtoMessage()    {}
func (*RaftCmdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_cmdpb_409ff26e8ae8c248, []int{23}
}
func (m *RaftCmdResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftCmdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftCmdResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RaftCmdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftCmdResponse.Merge(dst, src)
}
func (m *RaftCmdResponse) XXX_Size() int {
	return m.Size()
}
func (m *RaftCmdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftCmdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RaftCmdResponse proto.InternalMessageInfo

func (m *RaftCmdResponse) GetHeader() *RaftResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *RaftCmdResponse) GetResponses() []*Response {
	if m != nil {
		return m.Responses
	}
	return nil
}

func (m *RaftCmdResponse) GetAdminResponse() *AdminResponse {
	if m != nil {
		return m.AdminResponse
	}
	return nil
}

func init() {
	proto.RegisterType((*GetRequest)(nil), "raft_cmdpb.GetRequest")
	proto.RegisterType((*GetResponse)(nil), "raft_cmdpb.GetResponse")
	proto.RegisterType((*PutRequest)(nil), "raft_cmdpb.PutRequest")
	proto.RegisterType((*PutResponse)(nil), "raft_cmdpb.PutResponse")
	proto.RegisterType((*DeleteRequest)(nil), "raft_cmdpb.DeleteRequest")
	proto.RegisterType((*DeleteResponse)(nil), "raft_cmdpb.DeleteResponse")
	proto.RegisterType((*SnapRequest)(nil), "raft_cmdpb.SnapRequest")
	proto.RegisterType((*SnapResponse)(nil), "raft_cmdpb.SnapResponse")
	proto.RegisterType((*Request)(nil), "raft_cmdpb.Request")
	proto.RegisterType((*Response)(nil), "raft_cmdpb.Response")
	proto.RegisterType((*ChangePeerRequest)(nil), "raft_cmdpb.ChangePeerRequest")
	proto.RegisterType((*ChangePeerResponse)(nil), "raft_cmdpb.ChangePeerResponse")
	proto.RegisterType((*SplitRequest)(nil), "raft_cmdpb.SplitRequest")
	proto.RegisterType((*SplitResponse)(nil), "raft_cmdpb.SplitResponse")
	proto.RegisterType((*CompactLogRequest)(nil), "raft_cmdpb.CompactLogRequest")
	proto.RegisterType((*CompactLogResponse)(nil), "raft_cmdpb.CompactLogResponse")
	proto.RegisterType((*TransferLeaderRequest)(nil), "raft_cmdpb.TransferLeaderRequest")
	proto.RegisterType((*TransferLeaderResponse)(nil), "raft_cmdpb.TransferLeaderResponse")
	proto.RegisterType((*AdminRequest)(nil), "raft_cmdpb.AdminRequest")
	proto.RegisterType((*AdminResponse)(nil), "raft_cmdpb.AdminResponse")
	proto.RegisterType((*RaftRequestHeader)(nil), "raft_cmdpb.RaftRequestHeader")
	proto.RegisterType((*RaftResponseHeader)(nil), "raft_cmdpb.RaftResponseHeader")
	proto.RegisterType((*RaftCmdRequest)(nil), "raft_cmdpb.RaftCmdRequest")
	proto.RegisterType((*RaftCmdResponse)(nil), "raft_cmdpb.RaftCmdResponse")
	proto.RegisterEnum("raft_cmdpb.CmdType", CmdType_name, CmdType_value)
	proto.RegisterEnum("raft_cmdpb.AdminCmdType", AdminCmdType_name, AdminCmdType_value)
}
func (m *GetRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Cf) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Cf)))
		i += copy(dAtA[i:], m.Cf)
	}
	if len(m.Key) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Key)))
		i += copy(dAtA[i:], m.Key)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *GetResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Value)))
		i += copy(dAtA[i:], m.Value)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *PutRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Cf) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Cf)))
		i += copy(dAtA[i:], m.Cf)
	}
	if len(m.Key) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Key)))
		i += copy(dAtA[i:], m.Key)
	}
	if len(m.Value) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Value)))
		i += copy(dAtA[i:], m.Value)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *PutResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *DeleteRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Cf) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Cf)))
		i += copy(dAtA[i:], m.Cf)
	}
	if len(m.Key) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Key)))
		i += copy(dAtA[i:], m.Key)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *DeleteResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *SnapRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *SnapResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Region != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Region.Size()))
		n1, err := m.Region.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Request) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Request) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CmdType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CmdType))
	}
	if m.Get != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Get.Size()))
		n2, err := m.Get.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.Put != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Put.Size()))
		n3, err := m.Put.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.Delete != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Delete.Size()))
		n4, err := m.Delete.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.Snap != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Snap.Size()))
		n5, err := m.Snap.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Response) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Response) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CmdType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CmdType))
	}
	if m.Get != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Get.Size()))
		n6, err := m.Get.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.Put != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Put.Size()))
		n7, err := m.Put.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.Delete != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Delete.Size()))
		n8, err := m.Delete.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if m.Snap != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Snap.Size()))
		n9, err := m.Snap.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ChangePeerRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangePeerRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ChangeType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.ChangeType))
	}
	if m.Peer != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Peer.Size()))
		n10, err := m.Peer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *ChangePeerResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangePeerResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Region != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Region.Size()))
		n11, err := m.Region.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *SplitRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SplitRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SplitKey) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.SplitKey)))
		i += copy(dAtA[i:], m.SplitKey)
	}
	if m.NewRegionId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.NewRegionId))
	}
	if len(m.NewPeerIds) > 0 {
		dAtA13 := make([]byte, len(m.NewPeerIds)*10)
		var j12 int
		for _, num := range m.NewPeerIds {
			for num >= 1<<7 {
				dAtA13[j12] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j12++
			}
			dAtA13[j12] = uint8(num)
			j12++
		}
		dAtA[i] = 0x1a
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(j12))
		i += copy(dAtA[i:], dAtA13[:j12])
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *SplitResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SplitResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Regions) > 0 {
		for _, msg := range m.Regions {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *CompactLogRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CompactLogRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CompactIndex != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CompactIndex))
	}
	if m.CompactTerm != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CompactTerm))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *CompactLogResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CompactLogResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *TransferLeaderRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferLeaderRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Peer != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Peer.Size()))
		n14, err := m.Peer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *TransferLeaderResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferLeaderResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *AdminRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AdminRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CmdType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CmdType))
	}
	if m.ChangePeer != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.ChangePeer.Size()))
		n15, err := m.ChangePeer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	if m.CompactLog != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CompactLog.Size()))
		n16, err := m.CompactLog.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	if m.TransferLeader != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.TransferLeader.Size()))
		n17, err := m.TransferLeader.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	if m.Split != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Split.Size()))
		n18, err := m.Split.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *AdminResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AdminResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CmdType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CmdType))
	}
	if m.ChangePeer != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.ChangePeer.Size()))
		n19, err := m.ChangePeer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	if m.CompactLog != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CompactLog.Size()))
		n20, err := m.CompactLog.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n20
	}
	if m.TransferLeader != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.TransferLeader.Size()))
		n21, err := m.TransferLeader.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n21
	}
	if m.Split != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Split.Size()))
		n22, err := m.Split.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n22
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RaftRequestHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftRequestHeader) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.RegionId))
	}
	if m.Peer != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Peer.Size()))
		n23, err := m.Peer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n23
	}
	if m.RegionEpoch != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.RegionEpoch.Size()))
		n24, err := m.RegionEpoch.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n24
	}
	if m.Term != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Term))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RaftResponseHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftResponseHeader) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Error.Size()))
		n25, err := m.Error.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n25
	}
	if len(m.Uuid) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Uuid)))
		i += copy(dAtA[i:], m.Uuid)
	}
	if m.CurrentTerm != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CurrentTerm))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RaftCmdRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftCmdRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Header.Size()))
		n26, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n26
	}
	if len(m.Requests) > 0 {
		for _, msg := range m.Requests {
			dAtA[i] = 0x12
			i++
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.AdminRequest != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.AdminRequest.Size()))
		n27, err := m.AdminRequest.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n27
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RaftCmdResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftCmdResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Header.Size()))
		n28, err := m.Header.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n28
	}
	if len(m.Responses) > 0 {
		for _, msg := range m.Responses {
			dAtA[i] = 0x12
			i++
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.AdminResponse != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.AdminResponse.Size()))
		n29, err := m.AdminResponse.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n29
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func encodeVarintRaftCmdpb(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GetRequest) Size() (n int) {
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetResponse) Size() (n int) {
	var l int
	_ = l
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PutRequest) Size() (n int) {
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *PutResponse) Size() (n int) {
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DeleteRequest) Size() (n int) {
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DeleteResponse) Size() (n int) {
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SnapRequest) Size() (n int) {
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SnapResponse) Size() (n int) {
	var l int
	_ = l
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Request) Size() (n int) {
	var l int
	_ = l
	if m.CmdType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CmdType))
	}
	if m.Get != nil {
		l = m.Get.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Put != nil {
		l = m.Put.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Delete != nil {
		l = m.Delete.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Snap != nil {
		l = m.Snap.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Response) Size() (n int) {
	var l int
	_ = l
	if m.CmdType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CmdType))
	}
	if m.Get != nil {
		l = m.Get.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Put != nil {
		l = m.Put.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Delete != nil {
		l = m.Delete.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Snap != nil {
		l = m.Snap.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ChangePeerRequest) Size() (n int) {
	var l int
	_ = l
	if m.ChangeType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.ChangeType))
	}
	if m.Peer != nil {
		l = m.Peer.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ChangePeerResponse) Size() (n int) {
	var l int
	_ = l
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SplitRequest) Size() (n int) {
	var l int
	_ = l
	l = len(m.SplitKey)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.NewRegionId != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.NewRegionId))
	}
	if len(m.NewPeerIds) > 0 {
		l = 0
		for _, e := range m.NewPeerIds {
			l += sovRaftCmdpb(uint64(e))
		}
		n += 1 + sovRaftCmdpb(uint64(l)) + l
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SplitResponse) Size() (n int) {
	var l int
	_ = l
	if len(m.Regions) > 0 {
		for _, e := range m.Regions {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CompactLogRequest) Size() (n int) {
	var l int
	_ = l
	if m.CompactIndex != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CompactIndex))
	}
	if m.CompactTerm != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CompactTerm))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CompactLogResponse) Size() (n int) {
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *TransferLeaderRequest) Size() (n int) {
	var l int
	_ = l
	if m.Peer != nil {
		l = m.Peer.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *TransferLeaderResponse) Size() (n int) {
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *AdminRequest) Size() (n int) {
	var l int
	_ = l
	if m.CmdType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CmdType))
	}
	if m.ChangePeer != nil {
		l = m.ChangePeer.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.CompactLog != nil {
		l = m.CompactLog.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.TransferLeader != nil {
		l = m.TransferLeader.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Split != nil {
		l = m.Split.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *AdminResponse) Size() (n int) {
	var l int
	_ = l
	if m.CmdType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CmdType))
	}
	if m.ChangePeer != nil {
		l = m.ChangePeer.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.CompactLog != nil {
		l = m.CompactLog.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.TransferLeader != nil {
		l = m.TransferLeader.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Split != nil {
		l = m.Split.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RaftRequestHeader) Size() (n int) {
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.RegionId))
	}
	if m.Peer != nil {
		l = m.Peer.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Term != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.Term))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RaftResponseHeader) Size() (n int) {
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.CurrentTerm != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CurrentTerm))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RaftCmdRequest) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if len(m.Requests) > 0 {
		for _, e := range m.Requests {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	if m.AdminRequest != nil {
		l = m.AdminRequest.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RaftCmdResponse) Size() (n int) {
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if len(m.Responses) > 0 {
		for _, e := range m.Responses {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	if m.AdminResponse != nil {
		l = m.AdminResponse.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovRaftCmdpb(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozRaftCmdpb(x uint64) (n int) {
	return sovRaftCmdpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GetRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PutRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PutResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Request) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Request: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Request: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdType", wireType)
			}
			m.CmdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdType |= (CmdType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Get", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Get == nil {
				m.Get = &GetRequest{}
			}
			if err := m.Get.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Put", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Put == nil {
				m.Put = &PutRequest{}
			}
			if err := m.Put.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Delete", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Delete == nil {
				m.Delete = &DeleteRequest{}
			}
			if err := m.Delete.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Snap", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Snap == nil {
				m.Snap = &SnapRequest{}
			}
			if err := m.Snap.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Response) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Response: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Response: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdType", wireType)
			}
			m.CmdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdType |= (CmdType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Get", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Get == nil {
				m.Get = &GetResponse{}
			}
			if err := m.Get.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Put", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Put == nil {
				m.Put = &PutResponse{}
			}
			if err := m.Put.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Delete", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Delete == nil {
				m.Delete = &DeleteResponse{}
			}
			if err := m.Delete.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Snap", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Snap == nil {
				m.Snap = &SnapResponse{}
			}
			if err := m.Snap.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangePeerRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChangePeerRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChangePeerRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangeType", wireType)
			}
			m.ChangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeType |= (eraftpb.ConfChangeType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Peer == nil {
				m.Peer = &metapb.Peer{}
			}
			if err := m.Peer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangePeerResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChangePeerResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChangePeerResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SplitRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SplitRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SplitRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SplitKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SplitKey = append(m.SplitKey[:0], dAtA[iNdEx:postIndex]...)
			if m.SplitKey == nil {
				m.SplitKey = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewRegionId", wireType)
			}
			m.NewRegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewRegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRaftCmdpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.NewPeerIds = append(m.NewPeerIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRaftCmdpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRaftCmdpb
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRaftCmdpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.NewPeerIds = append(m.NewPeerIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field NewPeerIds", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SplitResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SplitResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SplitResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Regions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Regions = append(m.Regions, &metapb.Region{})
			if err := m.Regions[len(m.Regions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CompactLogRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CompactLogRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CompactLogRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CompactIndex", wireType)
			}
			m.CompactIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CompactIndex |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CompactTerm", wireType)
			}
			m.CompactTerm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CompactTerm |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CompactLogResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CompactLogResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CompactLogResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferLeaderRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TransferLeaderRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TransferLeaderRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Peer == nil {
				m.Peer = &metapb.Peer{}
			}
			if err := m.Peer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferLeaderResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TransferLeaderResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TransferLeaderResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AdminRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AdminRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AdminRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdType", wireType)
			}
			m.CmdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdType |= (AdminCmdType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangePeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ChangePeer == nil {
				m.ChangePeer = &ChangePeerRequest{}
			}
			if err := m.ChangePeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CompactLog", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CompactLog == nil {
				m.CompactLog = &CompactLogRequest{}
			}
			if err := m.CompactLog.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TransferLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TransferLeader == nil {
				m.TransferLeader = &TransferLeaderRequest{}
			}
			if err := m.TransferLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Split", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Split == nil {
				m.Split = &SplitRequest{}
			}
			if err := m.Split.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AdminResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AdminResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AdminResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdType", wireType)
			}
			m.CmdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdType |= (AdminCmdType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangePeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ChangePeer == nil {
				m.ChangePeer = &ChangePeerResponse{}
			}
			if err := m.ChangePeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CompactLog", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CompactLog == nil {
				m.CompactLog = &CompactLogResponse{}
			}
			if err := m.CompactLog.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TransferLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TransferLeader == nil {
				m.TransferLeader = &TransferLeaderResponse{}
			}
			if err := m.TransferLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Split", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Split == nil {
				m.Split = &SplitResponse{}
			}
			if err := m.Split.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftRequestHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftRequestHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftRequestHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Peer == nil {
				m.Peer = &metapb.Peer{}
			}
			if err := m.Peer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Term", wireType)
			}
			m.Term = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Term |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftResponseHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftResponseHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftResponseHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &errorpb.Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentTerm", wireType)
			}
			m.CurrentTerm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentTerm |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftCmdRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftCmdRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftCmdRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RaftRequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Requests", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Requests = append(m.Requests, &Request{})
			if err := m.Requests[len(m.Requests)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdminRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdminRequest == nil {
				m.AdminRequest = &AdminRequest{}
			}
			if err := m.AdminRequest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftCmdResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftCmdResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftCmdResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RaftResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Responses", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Responses = append(m.Responses, &Response{})
			if err := m.Responses[len(m.Responses)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdminResponse", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdminResponse == nil {
				m.AdminResponse = &AdminResponse{}
			}
			if err := m.AdminResponse.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipRaftCmdpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthRaftCmdpb
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowRaftCmdpb
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipRaftCmdpb(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthRaftCmdpb = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRaftCmdpb   = fmt.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("raft_cmdpb.proto", fileDescriptor_raft_cmdpb_409ff26e8ae8c248) }

var fileDescriptor_raft_cmdpb_409ff26e8ae8c248 = []byte{
	// 1068 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x56, 0xcd, 0x6e, 0xdb, 0x46,
	0x10, 0x0e, 0x45, 0xea, 0xc7, 0x43, 0x52, 0xa1, 0x37, 0x6e, 0x4c, 0x3b, 0xa8, 0xa0, 0x30, 0x45,
	0xe1, 0xa4, 0x85, 0x82, 0x38, 0xa8, 0xd1, 0x00, 0x6d, 0xd2, 0xd6, 0x09, 0x52, 0x37, 0x39, 0x18,
	0x1b, 0xdf, 0x7a, 0x20, 0x18, 0x72, 0x25, 0x0b, 0x95, 0x48, 0x9a, 0xa4, 0xe2, 0xfa, 0x4d, 0x7a,
	0xea, 0x6b, 0xf4, 0x98, 0x4b, 0x0f, 0x3d, 0xf6, 0x11, 0x0a, 0xf7, 0xdc, 0x4b, 0x9f, 0xa0, 0xd8,
	0xdd, 0x59, 0xfe, 0x48, 0x72, 0x9b, 0xf4, 0x24, 0xee, 0xec, 0xcc, 0xc7, 0x6f, 0xbe, 0x9d, 0x6f,
	0x45, 0x70, 0xb2, 0x60, 0x5c, 0xf8, 0xe1, 0x3c, 0x4a, 0x5f, 0x8f, 0xd2, 0x2c, 0x29, 0x12, 0x02,
	0x55, 0x64, 0xd7, 0x9a, 0xb3, 0x22, 0x50, 0x3b, 0xbb, 0x36, 0xcb, 0xb2, 0x24, 0xab, 0x2f, 0x83,
	0x71, 0xa1, 0x96, 0xde, 0x08, 0xe0, 0x39, 0x2b, 0x28, 0x3b, 0x5b, 0xb0, 0xbc, 0x20, 0x7d, 0x68,
	0x85, 0x63, 0x57, 0x1b, 0x6a, 0x7b, 0x1b, 0xb4, 0x15, 0x8e, 0x89, 0x03, 0xfa, 0x0f, 0xec, 0xc2,
	0x6d, 0x0d, 0xb5, 0x3d, 0x8b, 0xf2, 0x47, 0xef, 0x0e, 0x98, 0x22, 0x3f, 0x4f, 0x93, 0x38, 0x67,
	0x64, 0x0b, 0xda, 0x6f, 0x82, 0xd9, 0x82, 0x89, 0x1a, 0x8b, 0xca, 0x85, 0xf7, 0x14, 0xe0, 0x78,
	0xf1, 0xee, 0xa0, 0x15, 0x8a, 0x5e, 0x47, 0xb1, 0xc1, 0x14, 0x28, 0xf2, 0x55, 0xde, 0x03, 0xb0,
	0x9f, 0xb2, 0x19, 0x2b, 0xd8, 0xbb, 0x93, 0x75, 0xa0, 0xaf, 0x4a, 0x10, 0xc4, 0x06, 0xf3, 0x55,
	0x1c, 0xa4, 0x08, 0xe1, 0x1d, 0x80, 0x25, 0x97, 0xd8, 0xce, 0xc7, 0xd0, 0xc9, 0xd8, 0x64, 0x9a,
	0xc4, 0x02, 0xd6, 0xdc, 0xef, 0x8f, 0x50, 0x4a, 0x2a, 0xa2, 0x14, 0x77, 0xbd, 0xbf, 0x34, 0xe8,
	0x2a, 0x1a, 0x23, 0xe8, 0x85, 0xf3, 0xc8, 0x2f, 0x2e, 0x52, 0xa9, 0x42, 0x7f, 0xff, 0xc6, 0xa8,
	0x76, 0x3c, 0x87, 0xf3, 0xe8, 0xe4, 0x22, 0x65, 0xb4, 0x1b, 0xca, 0x07, 0xb2, 0x07, 0xfa, 0x84,
	0x15, 0x82, 0xa6, 0xb9, 0x7f, 0xb3, 0x9e, 0x5a, 0x1d, 0x04, 0xe5, 0x29, 0x3c, 0x33, 0x5d, 0x14,
	0xae, 0xb1, 0x9a, 0x59, 0xa9, 0x4b, 0x79, 0x0a, 0x79, 0x00, 0x9d, 0x48, 0x34, 0xea, 0xb6, 0x45,
	0xf2, 0x4e, 0x3d, 0xb9, 0xa1, 0x1a, 0xc5, 0x44, 0xf2, 0x09, 0x18, 0x79, 0x1c, 0xa4, 0x6e, 0x47,
	0x14, 0x6c, 0xd7, 0x0b, 0x6a, 0x0a, 0x51, 0x91, 0xe4, 0xfd, 0xad, 0x41, 0xaf, 0x14, 0xe9, 0x7d,
	0x1b, 0xbe, 0x5b, 0x6f, 0x78, 0x7b, 0xa5, 0x61, 0x89, 0x2a, 0x3b, 0xbe, 0x5b, 0xef, 0x78, 0x7b,
	0xa5, 0x63, 0x95, 0xca, 0x5b, 0xde, 0x5f, 0x6a, 0x79, 0x77, 0x5d, 0xcb, 0x58, 0xa0, 0x7a, 0xfe,
	0xb4, 0xd1, 0xb3, 0xbb, 0xda, 0x33, 0xe6, 0xcb, 0xa6, 0x13, 0xd8, 0x3c, 0x3c, 0x0d, 0xe2, 0x09,
	0x3b, 0x66, 0x2c, 0x53, 0xa7, 0xfd, 0x39, 0x98, 0xa1, 0x08, 0xd6, 0xfb, 0xdf, 0x1e, 0x29, 0x53,
	0x1d, 0x26, 0xf1, 0x58, 0x16, 0x09, 0x0d, 0x20, 0x2c, 0x9f, 0xc9, 0x10, 0x8c, 0x94, 0xb1, 0x0c,
	0x75, 0xb0, 0xd4, 0x64, 0x09, 0x70, 0xb1, 0xe3, 0x7d, 0x01, 0xa4, 0xfe, 0xc2, 0xf7, 0x9c, 0xc9,
	0x33, 0xb0, 0x5e, 0xa5, 0xb3, 0x69, 0x69, 0xbb, 0x5b, 0xb0, 0x91, 0xf3, 0xb5, 0xcf, 0x4d, 0x21,
	0xed, 0xd9, 0x13, 0x81, 0x17, 0xec, 0x82, 0x78, 0x60, 0xc7, 0xec, 0xdc, 0x97, 0xa5, 0xfe, 0x34,
	0x12, 0xac, 0x0c, 0x6a, 0xc6, 0xec, 0x5c, 0xc2, 0x1e, 0x45, 0x64, 0x08, 0x16, 0xcf, 0xe1, 0xd4,
	0xfc, 0x69, 0x94, 0xbb, 0xfa, 0x50, 0xdf, 0x33, 0x28, 0xc4, 0xec, 0x9c, 0xf3, 0x3b, 0x8a, 0x72,
	0xef, 0x11, 0xd8, 0xf8, 0x4a, 0xe4, 0xba, 0x07, 0x5d, 0x09, 0x99, 0xbb, 0xda, 0x50, 0x5f, 0x43,
	0x56, 0x6d, 0x7b, 0xdf, 0xc3, 0xe6, 0x61, 0x32, 0x4f, 0x83, 0xb0, 0x78, 0x99, 0x4c, 0x14, 0xe5,
	0x3b, 0x60, 0x87, 0x32, 0xe8, 0x4f, 0xe3, 0x88, 0xfd, 0x28, 0x68, 0x1b, 0xd4, 0xc2, 0xe0, 0x11,
	0x8f, 0x91, 0xdb, 0xa0, 0xd6, 0x7e, 0xc1, 0xb2, 0xb9, 0x62, 0x8e, 0xb1, 0x13, 0x96, 0xcd, 0xbd,
	0x2d, 0x20, 0x75, 0x70, 0xf4, 0xfe, 0x23, 0xf8, 0xe0, 0x24, 0x0b, 0xe2, 0x7c, 0xcc, 0xb2, 0x97,
	0x2c, 0x88, 0xaa, 0x33, 0x55, 0x27, 0xa3, 0x5d, 0x79, 0x32, 0x2e, 0xdc, 0x5c, 0x2e, 0x45, 0xd0,
	0xb7, 0x2d, 0xb0, 0xbe, 0x8e, 0xe6, 0xd3, 0x58, 0x81, 0x3d, 0x5c, 0x71, 0x47, 0x63, 0xce, 0x44,
	0xee, 0x8a, 0x45, 0x1e, 0x97, 0x53, 0x55, 0x1b, 0x91, 0x0f, 0x1b, 0xae, 0x5a, 0x9e, 0x44, 0x35,
	0x5b, 0x3c, 0x24, 0xea, 0x51, 0x93, 0x59, 0x32, 0x41, 0xff, 0x34, 0xeb, 0x97, 0xc5, 0xa6, 0x10,
	0x96, 0x21, 0xf2, 0x1d, 0x5c, 0x2f, 0xb0, 0x3f, 0x7f, 0x26, 0x1a, 0x44, 0x57, 0xdd, 0xae, 0x63,
	0xac, 0x55, 0x8f, 0xf6, 0x8b, 0x46, 0x98, 0x8c, 0xa0, 0x2d, 0xc6, 0xcc, 0x85, 0x35, 0x2e, 0xab,
	0x0d, 0x28, 0x95, 0x69, 0xde, 0xaf, 0x2d, 0xb0, 0x51, 0x41, 0x9c, 0xa2, 0xff, 0x25, 0xe1, 0x93,
	0x75, 0x12, 0x0e, 0xae, 0x92, 0x10, 0x8d, 0x5e, 0xd7, 0xf0, 0xc9, 0x3a, 0x0d, 0x07, 0x57, 0x69,
	0x58, 0x02, 0x54, 0x22, 0xbe, 0xb8, 0x4a, 0x44, 0xef, 0xdf, 0x44, 0x44, 0xa0, 0x65, 0x15, 0xef,
	0x37, 0x55, 0xdc, 0x59, 0xa3, 0x22, 0x56, 0xa2, 0x8c, 0x3f, 0x6b, 0xb0, 0x49, 0x83, 0xb1, 0x52,
	0xf7, 0x5b, 0x09, 0x73, 0x0b, 0x36, 0x2a, 0x8f, 0x4b, 0x37, 0xf5, 0xb2, 0xca, 0xe0, 0xff, 0x71,
	0x23, 0x91, 0x03, 0xb0, 0xb0, 0x9c, 0xa5, 0x49, 0x78, 0x8a, 0xa2, 0xdc, 0x68, 0x9a, 0xfa, 0x19,
	0xdf, 0xa2, 0x66, 0x56, 0x2d, 0x08, 0x01, 0x43, 0x78, 0xb3, 0x2d, 0xde, 0x28, 0x9e, 0xbd, 0x33,
	0x20, 0x92, 0x9f, 0xe4, 0x8d, 0x04, 0x3f, 0x82, 0xb6, 0xf8, 0x3e, 0x29, 0x2f, 0x37, 0xf5, 0xb5,
	0xf2, 0x8c, 0xff, 0x52, 0xb9, 0xc9, 0xf1, 0x16, 0x0b, 0xbc, 0xa5, 0x2c, 0x2a, 0x9e, 0xc5, 0x3d,
	0xb0, 0xc8, 0x32, 0x16, 0xe3, 0x3d, 0xa0, 0xe3, 0x3d, 0x20, 0x63, 0xe2, 0x1e, 0xf8, 0x45, 0x83,
	0x3e, 0x7f, 0xe7, 0xe1, 0x3c, 0x52, 0xf6, 0xfc, 0x0c, 0x3a, 0xa7, 0xf2, 0x6c, 0xb4, 0x55, 0x93,
	0xac, 0xe8, 0x47, 0x31, 0x99, 0xdc, 0x87, 0x5e, 0x26, 0x37, 0x72, 0xb7, 0x25, 0x6e, 0xb6, 0xc6,
	0x7f, 0x9e, 0x1a, 0xe9, 0x32, 0x89, 0x7c, 0x09, 0x76, 0xc0, 0xe7, 0xd4, 0xc7, 0x88, 0xa0, 0x67,
	0xae, 0x19, 0x64, 0x55, 0x6a, 0x05, 0xb5, 0x95, 0xf7, 0x56, 0x83, 0xeb, 0x25, 0x73, 0xb4, 0xc5,
	0xc1, 0x12, 0xf5, 0xc1, 0x2a, 0xf5, 0xba, 0xb4, 0x25, 0xf7, 0x7d, 0x3e, 0x03, 0x72, 0x47, 0x91,
	0xdf, 0x6a, 0x92, 0xc7, 0x49, 0xaa, 0xd2, 0xc8, 0x57, 0xd0, 0x57, 0xf4, 0x65, 0x08, 0xf9, 0xef,
	0xac, 0xe1, 0x8f, 0xd5, 0x76, 0x50, 0x5f, 0xde, 0x7b, 0x0c, 0x5d, 0xf4, 0x28, 0x31, 0xa1, 0x7b,
	0x14, 0xbf, 0x09, 0x66, 0xd3, 0xc8, 0xb9, 0x46, 0xba, 0xa0, 0x3f, 0x67, 0x85, 0xa3, 0xf1, 0x87,
	0xe3, 0x45, 0xe1, 0xe8, 0x04, 0xa0, 0x23, 0xff, 0xaf, 0x1d, 0x83, 0xf4, 0xc0, 0xe0, 0xff, 0xc4,
	0x4e, 0xfb, 0x9e, 0x8f, 0xf7, 0xaa, 0x02, 0x71, 0xc0, 0x42, 0x10, 0x11, 0x76, 0xae, 0x91, 0x3e,
	0x40, 0x65, 0x69, 0x47, 0x13, 0xeb, 0xd2, 0x8d, 0x8e, 0x4e, 0x08, 0xf4, 0x9b, 0x66, 0x73, 0x0c,
	0xb2, 0x01, 0x6d, 0xe1, 0x1e, 0x07, 0xbe, 0x71, 0x7e, 0xbb, 0x1c, 0x68, 0xbf, 0x5f, 0x0e, 0xb4,
	0x3f, 0x2e, 0x07, 0xda, 0x4f, 0x7f, 0x0e, 0xae, 0xbd, 0xee, 0x88, 0x4f, 0xe2, 0x87, 0xff, 0x04,
	0x00, 0x00, 0xff, 0xff, 0x26, 0xb1, 0x93, 0xfc, 0x5e, 0x0b, 0x00, 0x00,
}
