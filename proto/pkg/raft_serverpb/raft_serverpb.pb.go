// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: raft_serverpb.proto

package raft_serverpb

import (
	"fmt"
	"io"
	"math"

	proto "github.com/golang/protobuf/proto"

	eraftpb "github.com/pingcap-incubator/tinykv/proto/pkg/eraftpb"
	metapb "github.com/pingcap-incubator/tinykv/proto/pkg/metapb"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Normal indicates that this Peer is normal;
// Tombstone shows that this Peer has been removed from Region and cannot join in Raft Group.
type PeerState int32

const (
	PeerState_Normal    PeerState = 0
	PeerState_Tombstone PeerState = 2
)

var PeerState_name = map[int32]string{
	0: "Normal",
	2: "Tombstone",
}
var PeerState_value = map[string]int32{
	"Normal":    0,
	"Tombstone": 2,
}

func (x PeerState) String() string {
	return proto.EnumName(PeerState_name, int32(x))
}
func (PeerState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{0}
}

// The message sent between Raft peer, it wraps the raft meessage with some meta information.
type RaftMessage struct {
	RegionId    uint64              `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	FromPeer    *metapb.Peer        `protobuf:"bytes,2,opt,name=from_peer,json=fromPeer" json:"from_peer,omitempty"`
	ToPeer      *metapb.Peer        `protobuf:"bytes,3,opt,name=to_peer,json=toPeer" json:"to_peer,omitempty"`
	Message     *eraftpb.Message    `protobuf:"bytes,4,opt,name=message" json:"message,omitempty"`
	RegionEpoch *metapb.RegionEpoch `protobuf:"bytes,5,opt,name=region_epoch,json=regionEpoch" json:"region_epoch,omitempty"`
	// true means to_peer is a tombstone peer and it should remove itself.
	IsTombstone bool `protobuf:"varint,6,opt,name=is_tombstone,json=isTombstone,proto3" json:"is_tombstone,omitempty"`
	// Region key range [start_key, end_key). (Used in 3B)
	StartKey             []byte   `protobuf:"bytes,7,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey               []byte   `protobuf:"bytes,8,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RaftMessage) Reset()         { *m = RaftMessage{} }
func (m *RaftMessage) String() string { return proto.CompactTextString(m) }
func (*RaftMessage) ProtoMessage()    {}
func (*RaftMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{0}
}
func (m *RaftMessage) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftMessage.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RaftMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftMessage.Merge(dst, src)
}
func (m *RaftMessage) XXX_Size() int {
	return m.Size()
}
func (m *RaftMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftMessage.DiscardUnknown(m)
}

var xxx_messageInfo_RaftMessage proto.InternalMessageInfo

func (m *RaftMessage) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RaftMessage) GetFromPeer() *metapb.Peer {
	if m != nil {
		return m.FromPeer
	}
	return nil
}

func (m *RaftMessage) GetToPeer() *metapb.Peer {
	if m != nil {
		return m.ToPeer
	}
	return nil
}

func (m *RaftMessage) GetMessage() *eraftpb.Message {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *RaftMessage) GetRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *RaftMessage) GetIsTombstone() bool {
	if m != nil {
		return m.IsTombstone
	}
	return false
}

func (m *RaftMessage) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *RaftMessage) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

// Used to store the persistent state for Raft, including the hard state for raft and the last index of the raft log.
type RaftLocalState struct {
	HardState            *eraftpb.HardState `protobuf:"bytes,1,opt,name=hard_state,json=hardState" json:"hard_state,omitempty"`
	LastIndex            uint64             `protobuf:"varint,2,opt,name=last_index,json=lastIndex,proto3" json:"last_index,omitempty"`
	LastTerm             uint64             `protobuf:"varint,3,opt,name=last_term,json=lastTerm,proto3" json:"last_term,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *RaftLocalState) Reset()         { *m = RaftLocalState{} }
func (m *RaftLocalState) String() string { return proto.CompactTextString(m) }
func (*RaftLocalState) ProtoMessage()    {}
func (*RaftLocalState) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{1}
}
func (m *RaftLocalState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftLocalState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftLocalState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RaftLocalState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftLocalState.Merge(dst, src)
}
func (m *RaftLocalState) XXX_Size() int {
	return m.Size()
}
func (m *RaftLocalState) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftLocalState.DiscardUnknown(m)
}

var xxx_messageInfo_RaftLocalState proto.InternalMessageInfo

func (m *RaftLocalState) GetHardState() *eraftpb.HardState {
	if m != nil {
		return m.HardState
	}
	return nil
}

func (m *RaftLocalState) GetLastIndex() uint64 {
	if m != nil {
		return m.LastIndex
	}
	return 0
}

func (m *RaftLocalState) GetLastTerm() uint64 {
	if m != nil {
		return m.LastTerm
	}
	return 0
}

// Used to store the persistent state for Raft state machine.
type RaftApplyState struct {
	// Record the applied index of the state machine to make sure
	// not apply any index twice after restart.
	AppliedIndex uint64 `protobuf:"varint,1,opt,name=applied_index,json=appliedIndex,proto3" json:"applied_index,omitempty"`
	// Record the index and term of the last raft log that have been truncated. (Used in 2C)
	TruncatedState       *RaftTruncatedState `protobuf:"bytes,2,opt,name=truncated_state,json=truncatedState" json:"truncated_state,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *RaftApplyState) Reset()         { *m = RaftApplyState{} }
func (m *RaftApplyState) String() string { return proto.CompactTextString(m) }
func (*RaftApplyState) ProtoMessage()    {}
func (*RaftApplyState) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{2}
}
func (m *RaftApplyState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftApplyState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftApplyState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RaftApplyState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftApplyState.Merge(dst, src)
}
func (m *RaftApplyState) XXX_Size() int {
	return m.Size()
}
func (m *RaftApplyState) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftApplyState.DiscardUnknown(m)
}

var xxx_messageInfo_RaftApplyState proto.InternalMessageInfo

func (m *RaftApplyState) GetAppliedIndex() uint64 {
	if m != nil {
		return m.AppliedIndex
	}
	return 0
}

func (m *RaftApplyState) GetTruncatedState() *RaftTruncatedState {
	if m != nil {
		return m.TruncatedState
	}
	return nil
}

// The truncated state for Raft log compaction.
type RaftTruncatedState struct {
	Index                uint64   `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Term                 uint64   `protobuf:"varint,2,opt,name=term,proto3" json:"term,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RaftTruncatedState) Reset()         { *m = RaftTruncatedState{} }
func (m *RaftTruncatedState) String() string { return proto.CompactTextString(m) }
func (*RaftTruncatedState) ProtoMessage()    {}
func (*RaftTruncatedState) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{3}
}
func (m *RaftTruncatedState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftTruncatedState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftTruncatedState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RaftTruncatedState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftTruncatedState.Merge(dst, src)
}
func (m *RaftTruncatedState) XXX_Size() int {
	return m.Size()
}
func (m *RaftTruncatedState) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftTruncatedState.DiscardUnknown(m)
}

var xxx_messageInfo_RaftTruncatedState proto.InternalMessageInfo

func (m *RaftTruncatedState) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *RaftTruncatedState) GetTerm() uint64 {
	if m != nil {
		return m.Term
	}
	return 0
}

// Used to store Region information and the corresponding Peer state on this Store.
type RegionLocalState struct {
	State                PeerState      `protobuf:"varint,1,opt,name=state,proto3,enum=raft_serverpb.PeerState" json:"state,omitempty"`
	Region               *metapb.Region `protobuf:"bytes,2,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RegionLocalState) Reset()         { *m = RegionLocalState{} }
func (m *RegionLocalState) String() string { return proto.CompactTextString(m) }
func (*RegionLocalState) ProtoMessage()    {}
func (*RegionLocalState) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{4}
}
func (m *RegionLocalState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionLocalState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionLocalState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RegionLocalState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionLocalState.Merge(dst, src)
}
func (m *RegionLocalState) XXX_Size() int {
	return m.Size()
}
func (m *RegionLocalState) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionLocalState.DiscardUnknown(m)
}

var xxx_messageInfo_RegionLocalState proto.InternalMessageInfo

func (m *RegionLocalState) GetState() PeerState {
	if m != nil {
		return m.State
	}
	return PeerState_Normal
}

func (m *RegionLocalState) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

// The persistent identification for Store.
// It used to recover the store id after restart.
type StoreIdent struct {
	ClusterId            uint64   `protobuf:"varint,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	StoreId              uint64   `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreIdent) Reset()         { *m = StoreIdent{} }
func (m *StoreIdent) String() string { return proto.CompactTextString(m) }
func (*StoreIdent) ProtoMessage()    {}
func (*StoreIdent) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{5}
}
func (m *StoreIdent) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreIdent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreIdent.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *StoreIdent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreIdent.Merge(dst, src)
}
func (m *StoreIdent) XXX_Size() int {
	return m.Size()
}
func (m *StoreIdent) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreIdent.DiscardUnknown(m)
}

var xxx_messageInfo_StoreIdent proto.InternalMessageInfo

func (m *StoreIdent) GetClusterId() uint64 {
	if m != nil {
		return m.ClusterId
	}
	return 0
}

func (m *StoreIdent) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

// Snapshot sending and reciveing related messages.
// Not included in the course scope.
type KeyValue struct {
	Key                  []byte   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value                []byte   `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KeyValue) Reset()         { *m = KeyValue{} }
func (m *KeyValue) String() string { return proto.CompactTextString(m) }
func (*KeyValue) ProtoMessage()    {}
func (*KeyValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{6}
}
func (m *KeyValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KeyValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KeyValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *KeyValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeyValue.Merge(dst, src)
}
func (m *KeyValue) XXX_Size() int {
	return m.Size()
}
func (m *KeyValue) XXX_DiscardUnknown() {
	xxx_messageInfo_KeyValue.DiscardUnknown(m)
}

var xxx_messageInfo_KeyValue proto.InternalMessageInfo

func (m *KeyValue) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *KeyValue) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type RaftSnapshotData struct {
	Region               *metapb.Region `protobuf:"bytes,1,opt,name=region" json:"region,omitempty"`
	FileSize             uint64         `protobuf:"varint,2,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	Data                 []*KeyValue    `protobuf:"bytes,3,rep,name=data" json:"data,omitempty"`
	Meta                 *SnapshotMeta  `protobuf:"bytes,5,opt,name=meta" json:"meta,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RaftSnapshotData) Reset()         { *m = RaftSnapshotData{} }
func (m *RaftSnapshotData) String() string { return proto.CompactTextString(m) }
func (*RaftSnapshotData) ProtoMessage()    {}
func (*RaftSnapshotData) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{7}
}
func (m *RaftSnapshotData) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftSnapshotData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftSnapshotData.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *RaftSnapshotData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftSnapshotData.Merge(dst, src)
}
func (m *RaftSnapshotData) XXX_Size() int {
	return m.Size()
}
func (m *RaftSnapshotData) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftSnapshotData.DiscardUnknown(m)
}

var xxx_messageInfo_RaftSnapshotData proto.InternalMessageInfo

func (m *RaftSnapshotData) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *RaftSnapshotData) GetFileSize() uint64 {
	if m != nil {
		return m.FileSize
	}
	return 0
}

func (m *RaftSnapshotData) GetData() []*KeyValue {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *RaftSnapshotData) GetMeta() *SnapshotMeta {
	if m != nil {
		return m.Meta
	}
	return nil
}

type SnapshotCFFile struct {
	Cf                   string   `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	Size_                uint64   `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Checksum             uint32   `protobuf:"varint,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SnapshotCFFile) Reset()         { *m = SnapshotCFFile{} }
func (m *SnapshotCFFile) String() string { return proto.CompactTextString(m) }
func (*SnapshotCFFile) ProtoMessage()    {}
func (*SnapshotCFFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{8}
}
func (m *SnapshotCFFile) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotCFFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotCFFile.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *SnapshotCFFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotCFFile.Merge(dst, src)
}
func (m *SnapshotCFFile) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotCFFile) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotCFFile.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotCFFile proto.InternalMessageInfo

func (m *SnapshotCFFile) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *SnapshotCFFile) GetSize_() uint64 {
	if m != nil {
		return m.Size_
	}
	return 0
}

func (m *SnapshotCFFile) GetChecksum() uint32 {
	if m != nil {
		return m.Checksum
	}
	return 0
}

type SnapshotMeta struct {
	CfFiles              []*SnapshotCFFile `protobuf:"bytes,1,rep,name=cf_files,json=cfFiles" json:"cf_files,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SnapshotMeta) Reset()         { *m = SnapshotMeta{} }
func (m *SnapshotMeta) String() string { return proto.CompactTextString(m) }
func (*SnapshotMeta) ProtoMessage()    {}
func (*SnapshotMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{9}
}
func (m *SnapshotMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *SnapshotMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotMeta.Merge(dst, src)
}
func (m *SnapshotMeta) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotMeta.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotMeta proto.InternalMessageInfo

func (m *SnapshotMeta) GetCfFiles() []*SnapshotCFFile {
	if m != nil {
		return m.CfFiles
	}
	return nil
}

type SnapshotChunk struct {
	Message              *RaftMessage `protobuf:"bytes,1,opt,name=message" json:"message,omitempty"`
	Data                 []byte       `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SnapshotChunk) Reset()         { *m = SnapshotChunk{} }
func (m *SnapshotChunk) String() string { return proto.CompactTextString(m) }
func (*SnapshotChunk) ProtoMessage()    {}
func (*SnapshotChunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{10}
}
func (m *SnapshotChunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotChunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotChunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *SnapshotChunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotChunk.Merge(dst, src)
}
func (m *SnapshotChunk) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotChunk) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotChunk.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotChunk proto.InternalMessageInfo

func (m *SnapshotChunk) GetMessage() *RaftMessage {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *SnapshotChunk) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type Done struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Done) Reset()         { *m = Done{} }
func (m *Done) String() string { return proto.CompactTextString(m) }
func (*Done) ProtoMessage()    {}
func (*Done) Descriptor() ([]byte, []int) {
	return fileDescriptor_raft_serverpb_9d4bf28a94e26664, []int{11}
}
func (m *Done) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Done) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Done.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalTo(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (dst *Done) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Done.Merge(dst, src)
}
func (m *Done) XXX_Size() int {
	return m.Size()
}
func (m *Done) XXX_DiscardUnknown() {
	xxx_messageInfo_Done.DiscardUnknown(m)
}

var xxx_messageInfo_Done proto.InternalMessageInfo

func init() {
	proto.RegisterType((*RaftMessage)(nil), "raft_serverpb.RaftMessage")
	proto.RegisterType((*RaftLocalState)(nil), "raft_serverpb.RaftLocalState")
	proto.RegisterType((*RaftApplyState)(nil), "raft_serverpb.RaftApplyState")
	proto.RegisterType((*RaftTruncatedState)(nil), "raft_serverpb.RaftTruncatedState")
	proto.RegisterType((*RegionLocalState)(nil), "raft_serverpb.RegionLocalState")
	proto.RegisterType((*StoreIdent)(nil), "raft_serverpb.StoreIdent")
	proto.RegisterType((*KeyValue)(nil), "raft_serverpb.KeyValue")
	proto.RegisterType((*RaftSnapshotData)(nil), "raft_serverpb.RaftSnapshotData")
	proto.RegisterType((*SnapshotCFFile)(nil), "raft_serverpb.SnapshotCFFile")
	proto.RegisterType((*SnapshotMeta)(nil), "raft_serverpb.SnapshotMeta")
	proto.RegisterType((*SnapshotChunk)(nil), "raft_serverpb.SnapshotChunk")
	proto.RegisterType((*Done)(nil), "raft_serverpb.Done")
	proto.RegisterEnum("raft_serverpb.PeerState", PeerState_name, PeerState_value)
}
func (m *RaftMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.RegionId))
	}
	if m.FromPeer != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.FromPeer.Size()))
		n1, err := m.FromPeer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.ToPeer != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.ToPeer.Size()))
		n2, err := m.ToPeer.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.Message != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Message.Size()))
		n3, err := m.Message.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.RegionEpoch != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.RegionEpoch.Size()))
		n4, err := m.RegionEpoch.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if m.IsTombstone {
		dAtA[i] = 0x30
		i++
		if m.IsTombstone {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if len(m.StartKey) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.StartKey)))
		i += copy(dAtA[i:], m.StartKey)
	}
	if len(m.EndKey) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.EndKey)))
		i += copy(dAtA[i:], m.EndKey)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RaftLocalState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftLocalState) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.HardState != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.HardState.Size()))
		n5, err := m.HardState.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.LastIndex != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.LastIndex))
	}
	if m.LastTerm != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.LastTerm))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RaftApplyState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftApplyState) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.AppliedIndex != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.AppliedIndex))
	}
	if m.TruncatedState != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.TruncatedState.Size()))
		n6, err := m.TruncatedState.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RaftTruncatedState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftTruncatedState) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Index != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Index))
	}
	if m.Term != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Term))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RegionLocalState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionLocalState) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.State != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.State))
	}
	if m.Region != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Region.Size()))
		n7, err := m.Region.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *StoreIdent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreIdent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ClusterId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.ClusterId))
	}
	if m.StoreId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.StoreId))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *KeyValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KeyValue) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Key) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Key)))
		i += copy(dAtA[i:], m.Key)
	}
	if len(m.Value) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Value)))
		i += copy(dAtA[i:], m.Value)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *RaftSnapshotData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftSnapshotData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Region != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Region.Size()))
		n8, err := m.Region.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if m.FileSize != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.FileSize))
	}
	if len(m.Data) > 0 {
		for _, msg := range m.Data {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintRaftServerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Meta != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Meta.Size()))
		n9, err := m.Meta.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *SnapshotCFFile) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotCFFile) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Cf) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Cf)))
		i += copy(dAtA[i:], m.Cf)
	}
	if m.Size_ != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Size_))
	}
	if m.Checksum != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Checksum))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *SnapshotMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotMeta) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CfFiles) > 0 {
		for _, msg := range m.CfFiles {
			dAtA[i] = 0xa
			i++
			i = encodeVarintRaftServerpb(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *SnapshotChunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotChunk) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Message != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Message.Size()))
		n10, err := m.Message.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if len(m.Data) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Done) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Done) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func encodeVarintRaftServerpb(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *RaftMessage) Size() (n int) {
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.RegionId))
	}
	if m.FromPeer != nil {
		l = m.FromPeer.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.ToPeer != nil {
		l = m.ToPeer.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.Message != nil {
		l = m.Message.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.IsTombstone {
		n += 2
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RaftLocalState) Size() (n int) {
	var l int
	_ = l
	if m.HardState != nil {
		l = m.HardState.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.LastIndex != 0 {
		n += 1 + sovRaftServerpb(uint64(m.LastIndex))
	}
	if m.LastTerm != 0 {
		n += 1 + sovRaftServerpb(uint64(m.LastTerm))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RaftApplyState) Size() (n int) {
	var l int
	_ = l
	if m.AppliedIndex != 0 {
		n += 1 + sovRaftServerpb(uint64(m.AppliedIndex))
	}
	if m.TruncatedState != nil {
		l = m.TruncatedState.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RaftTruncatedState) Size() (n int) {
	var l int
	_ = l
	if m.Index != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Index))
	}
	if m.Term != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Term))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RegionLocalState) Size() (n int) {
	var l int
	_ = l
	if m.State != 0 {
		n += 1 + sovRaftServerpb(uint64(m.State))
	}
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StoreIdent) Size() (n int) {
	var l int
	_ = l
	if m.ClusterId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.ClusterId))
	}
	if m.StoreId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.StoreId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *KeyValue) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RaftSnapshotData) Size() (n int) {
	var l int
	_ = l
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.FileSize != 0 {
		n += 1 + sovRaftServerpb(uint64(m.FileSize))
	}
	if len(m.Data) > 0 {
		for _, e := range m.Data {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SnapshotCFFile) Size() (n int) {
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.Size_ != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Size_))
	}
	if m.Checksum != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Checksum))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SnapshotMeta) Size() (n int) {
	var l int
	_ = l
	if len(m.CfFiles) > 0 {
		for _, e := range m.CfFiles {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SnapshotChunk) Size() (n int) {
	var l int
	_ = l
	if m.Message != nil {
		l = m.Message.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Done) Size() (n int) {
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovRaftServerpb(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozRaftServerpb(x uint64) (n int) {
	return sovRaftServerpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RaftMessage) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromPeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FromPeer == nil {
				m.FromPeer = &metapb.Peer{}
			}
			if err := m.FromPeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ToPeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ToPeer == nil {
				m.ToPeer = &metapb.Peer{}
			}
			if err := m.ToPeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Message == nil {
				m.Message = &eraftpb.Message{}
			}
			if err := m.Message.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsTombstone", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsTombstone = bool(v != 0)
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftLocalState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftLocalState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftLocalState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HardState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.HardState == nil {
				m.HardState = &eraftpb.HardState{}
			}
			if err := m.HardState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastIndex", wireType)
			}
			m.LastIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastIndex |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastTerm", wireType)
			}
			m.LastTerm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastTerm |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftApplyState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftApplyState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftApplyState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppliedIndex", wireType)
			}
			m.AppliedIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppliedIndex |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TruncatedState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TruncatedState == nil {
				m.TruncatedState = &RaftTruncatedState{}
			}
			if err := m.TruncatedState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftTruncatedState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftTruncatedState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftTruncatedState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Term", wireType)
			}
			m.Term = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Term |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionLocalState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionLocalState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionLocalState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= (PeerState(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreIdent) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreIdent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreIdent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterId", wireType)
			}
			m.ClusterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClusterId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KeyValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KeyValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KeyValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftSnapshotData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftSnapshotData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftSnapshotData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileSize", wireType)
			}
			m.FileSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FileSize |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data, &KeyValue{})
			if err := m.Data[len(m.Data)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Meta == nil {
				m.Meta = &SnapshotMeta{}
			}
			if err := m.Meta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotCFFile) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotCFFile: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotCFFile: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Checksum", wireType)
			}
			m.Checksum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Checksum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CfFiles", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CfFiles = append(m.CfFiles, &SnapshotCFFile{})
			if err := m.CfFiles[len(m.CfFiles)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotChunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotChunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotChunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Message == nil {
				m.Message = &RaftMessage{}
			}
			if err := m.Message.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Done) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Done: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Done: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipRaftServerpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthRaftServerpb
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowRaftServerpb
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipRaftServerpb(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthRaftServerpb = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRaftServerpb   = fmt.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("raft_serverpb.proto", fileDescriptor_raft_serverpb_9d4bf28a94e26664) }

var fileDescriptor_raft_serverpb_9d4bf28a94e26664 = []byte{
	// 751 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x54, 0xdd, 0x6e, 0xeb, 0x44,
	0x10, 0x3e, 0x4e, 0xdc, 0xc4, 0x9e, 0x38, 0x21, 0xda, 0x83, 0x74, 0x4c, 0x8e, 0x4e, 0x94, 0x1a,
	0x51, 0x85, 0x22, 0x05, 0x11, 0x10, 0xe2, 0x0a, 0x09, 0x28, 0x55, 0x43, 0x29, 0xaa, 0x36, 0x15,
	0x12, 0x57, 0xd6, 0xd6, 0x1e, 0x37, 0xa6, 0x8e, 0x6d, 0xed, 0x6e, 0x2a, 0xd2, 0x1b, 0xc4, 0x5b,
	0xf0, 0x22, 0x5c, 0xf0, 0x06, 0x5c, 0xf2, 0x08, 0xa8, 0xbc, 0x08, 0xda, 0x5d, 0x3b, 0x3f, 0x6d,
	0x39, 0x57, 0xd9, 0x99, 0xef, 0xf3, 0xec, 0x37, 0xdf, 0x4c, 0x16, 0x5e, 0x72, 0x96, 0xc8, 0x50,
	0x20, 0xbf, 0x43, 0x5e, 0x5e, 0x4f, 0x4a, 0x5e, 0xc8, 0x82, 0x74, 0xf7, 0x92, 0x83, 0x2e, 0xaa,
	0xb8, 0x46, 0x07, 0xde, 0x12, 0x25, 0xab, 0xa3, 0xe0, 0xcf, 0x06, 0x74, 0x28, 0x4b, 0xe4, 0x05,
	0x0a, 0xc1, 0x6e, 0x90, 0xbc, 0x06, 0x97, 0xe3, 0x4d, 0x5a, 0xe4, 0x61, 0x1a, 0xfb, 0xd6, 0xc8,
	0x1a, 0xdb, 0xd4, 0x31, 0x89, 0x59, 0x4c, 0x3e, 0x04, 0x37, 0xe1, 0xc5, 0x32, 0x2c, 0x11, 0xb9,
	0xdf, 0x18, 0x59, 0xe3, 0xce, 0xd4, 0x9b, 0x54, 0xe5, 0x2e, 0x11, 0x39, 0x75, 0x14, 0xac, 0x4e,
	0xe4, 0x03, 0x68, 0xcb, 0xc2, 0x10, 0x9b, 0xcf, 0x10, 0x5b, 0xb2, 0xd0, 0xb4, 0x63, 0x68, 0x2f,
	0xcd, 0xcd, 0xbe, 0xad, 0x69, 0xfd, 0x49, 0xad, 0xb6, 0x52, 0x44, 0x6b, 0x02, 0xf9, 0x1c, 0xbc,
	0x4a, 0x1a, 0x96, 0x45, 0xb4, 0xf0, 0x0f, 0xf4, 0x07, 0x2f, 0xeb, 0xba, 0x54, 0x63, 0xdf, 0x2a,
	0x88, 0x76, 0xf8, 0x36, 0x20, 0x87, 0xe0, 0xa5, 0x22, 0x94, 0xc5, 0xf2, 0x5a, 0xc8, 0x22, 0x47,
	0xbf, 0x35, 0xb2, 0xc6, 0x0e, 0xed, 0xa4, 0xe2, 0xaa, 0x4e, 0xa9, 0xae, 0x85, 0x64, 0x5c, 0x86,
	0xb7, 0xb8, 0xf6, 0xdb, 0x23, 0x6b, 0xec, 0x51, 0x47, 0x27, 0xce, 0x71, 0x4d, 0x5e, 0x41, 0x1b,
	0xf3, 0x58, 0x43, 0x8e, 0x86, 0x5a, 0x98, 0xc7, 0xe7, 0xb8, 0x0e, 0x7e, 0x85, 0x9e, 0xb2, 0xee,
	0xfb, 0x22, 0x62, 0xd9, 0x5c, 0x32, 0x89, 0xe4, 0x13, 0x80, 0x05, 0xe3, 0x71, 0x28, 0x54, 0xa4,
	0xed, 0xeb, 0x4c, 0xc9, 0xa6, 0xa3, 0x33, 0xc6, 0x63, 0xcd, 0xa3, 0xee, 0xa2, 0x3e, 0x92, 0x37,
	0x00, 0x19, 0x13, 0x32, 0x4c, 0xf3, 0x18, 0x7f, 0xd1, 0xa6, 0xda, 0xd4, 0x55, 0x99, 0x99, 0x4a,
	0x28, 0x65, 0x1a, 0x96, 0xc8, 0x97, 0xda, 0x49, 0x9b, 0x3a, 0x2a, 0x71, 0x85, 0x7c, 0x19, 0xfc,
	0x66, 0x19, 0x05, 0x5f, 0x95, 0x65, 0xb6, 0x36, 0xe5, 0xde, 0x87, 0x2e, 0x2b, 0xcb, 0x2c, 0xc5,
	0xb8, 0xaa, 0x68, 0x66, 0xe8, 0x55, 0x49, 0x53, 0xf4, 0x3b, 0x78, 0x47, 0xf2, 0x55, 0x1e, 0x31,
	0x89, 0xb5, 0x56, 0x33, 0xcd, 0xc3, 0xc9, 0xfe, 0x3e, 0xa9, 0xe2, 0x57, 0x35, 0xd3, 0x48, 0xef,
	0xc9, 0xbd, 0x38, 0xf8, 0x12, 0xc8, 0x53, 0x16, 0x79, 0x17, 0x0e, 0x76, 0xaf, 0x37, 0x01, 0x21,
	0x60, 0xeb, 0x3e, 0x4c, 0x97, 0xfa, 0x1c, 0xfc, 0x0c, 0x7d, 0x33, 0xb9, 0x1d, 0x1b, 0x27, 0x70,
	0xb0, 0x75, 0xb0, 0x37, 0xf5, 0x1f, 0xa9, 0x52, 0x9b, 0x63, 0xc4, 0x18, 0x1a, 0x39, 0x82, 0x96,
	0x19, 0x78, 0xd5, 0x46, 0x6f, 0x7f, 0x27, 0x68, 0x85, 0x06, 0xa7, 0x00, 0x73, 0x59, 0x70, 0x9c,
	0xc5, 0x98, 0x4b, 0xe5, 0x7c, 0x94, 0xad, 0x84, 0x44, 0xbe, 0xdd, 0x75, 0xb7, 0xca, 0xcc, 0x62,
	0xf2, 0x1e, 0x38, 0x42, 0x91, 0x15, 0x68, 0x04, 0xb7, 0x85, 0xf9, 0x38, 0x98, 0x82, 0x73, 0x8e,
	0xeb, 0x1f, 0x59, 0xb6, 0x42, 0xd2, 0x87, 0xa6, 0xda, 0x0c, 0x4b, 0x6f, 0x86, 0x3a, 0xaa, 0xde,
	0xef, 0x14, 0xa4, 0xbf, 0xf2, 0xa8, 0x09, 0x82, 0x3f, 0x2c, 0xe8, 0x2b, 0xa3, 0xe6, 0x39, 0x2b,
	0xc5, 0xa2, 0x90, 0x27, 0x4c, 0xb2, 0x1d, 0xe1, 0xd6, 0xdb, 0x84, 0xab, 0x2d, 0x48, 0xd2, 0x0c,
	0x43, 0x91, 0xde, 0x63, 0x25, 0xc6, 0x51, 0x89, 0x79, 0x7a, 0x8f, 0xe4, 0x23, 0xb0, 0x63, 0x26,
	0x99, 0xdf, 0x1c, 0x35, 0xc7, 0x9d, 0xe9, 0xab, 0x47, 0x66, 0xd5, 0x42, 0xa9, 0x26, 0x91, 0x8f,
	0xc1, 0x56, 0x57, 0x54, 0x7f, 0x9e, 0xd7, 0x8f, 0xc8, 0xb5, 0xb8, 0x0b, 0x94, 0x8c, 0x6a, 0x62,
	0x70, 0x09, 0xbd, 0x3a, 0xfb, 0xcd, 0xe9, 0x69, 0x9a, 0x21, 0xe9, 0x41, 0x23, 0x4a, 0xb4, 0x60,
	0x97, 0x36, 0xa2, 0x44, 0x4d, 0x75, 0x47, 0x97, 0x3e, 0x93, 0x01, 0x38, 0xd1, 0x02, 0xa3, 0x5b,
	0xb1, 0x32, 0x5b, 0xdb, 0xa5, 0x9b, 0x38, 0x38, 0x03, 0x6f, 0xf7, 0x1e, 0xf2, 0x05, 0x38, 0x51,
	0x12, 0xaa, 0x76, 0x84, 0x6f, 0xe9, 0x1e, 0xde, 0xfc, 0x8f, 0x2c, 0x23, 0x80, 0xb6, 0xa3, 0x44,
	0xfd, 0x8a, 0xe0, 0x27, 0xe8, 0x6e, 0xa0, 0xc5, 0x2a, 0xbf, 0x25, 0x9f, 0x6d, 0x9f, 0x13, 0x63,
	0xe8, 0xe0, 0x99, 0x85, 0x7e, 0xf2, 0xb0, 0x90, 0xca, 0x40, 0x33, 0x2f, 0x7d, 0x0e, 0x5a, 0x60,
	0x9f, 0x14, 0x39, 0x1e, 0x1f, 0x81, 0xbb, 0x59, 0x37, 0x02, 0xd0, 0xfa, 0xa1, 0xe0, 0x4b, 0x96,
	0xf5, 0x5f, 0x90, 0x2e, 0xb8, 0x9b, 0xf7, 0xa3, 0xdf, 0xf8, 0xba, 0xff, 0xd7, 0xc3, 0xd0, 0xfa,
	0xfb, 0x61, 0x68, 0xfd, 0xf3, 0x30, 0xb4, 0x7e, 0xff, 0x77, 0xf8, 0xe2, 0xba, 0xa5, 0x1f, 0xd8,
	0x4f, 0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0x79, 0xcc, 0xfd, 0xcf, 0xa3, 0x05, 0x00, 0x00,
}
