syntax = "proto3";
package errorpb;

import "metapb.proto";
import "gogoproto/gogo.proto";

option (gogoproto.marshaler_all) = true;
option (gogoproto.sizer_all) = true;
option (gogoproto.unmarshaler_all) = true;

message NotLeader {
    uint64 region_id = 1;
    metapb.Peer leader = 2;
}

message StoreNotMatch {
    uint64 request_store_id = 1;
    uint64 actual_store_id = 2;
}

message RegionNotFound {
    uint64 region_id = 1;
}

message KeyNotInRegion {
    bytes key = 1;
    uint64 region_id = 2;
    bytes start_key = 3;
    bytes end_key = 4;
}

message EpochNotMatch {
    repeated metapb.Region current_regions = 1;
}

message StaleCommand {
}

message Error {
    reserved "stale_epoch";

    string message = 1;
    NotLeader not_leader = 2;
    RegionNotFound region_not_found = 3;
    KeyNotInRegion key_not_in_region = 4;
    EpochNotMatch epoch_not_match = 5;
    StaleCommand stale_command = 7;
    StoreNotMatch store_not_match = 8;
}