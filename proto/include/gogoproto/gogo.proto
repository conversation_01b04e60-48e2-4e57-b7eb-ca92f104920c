// Protocol Buffers for Go with Gadgets
//
// Copyright (c) 2013, The GoGo Authors. All rights reserved.
// http://github.com/gogo/protobuf
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto2";
package gogoproto;

import "google/protobuf/descriptor.proto";

option java_package = "com.google.protobuf";
option java_outer_classname = "GoGoProtos";
option go_package = "github.com/gogo/protobuf/gogoproto";

extend google.protobuf.EnumOptions {
	optional bool goproto_enum_prefix = 62001;
	optional bool goproto_enum_stringer = 62021;
	optional bool enum_stringer = 62022;
	optional string enum_customname = 62023;
	optional bool enumdecl = 62024;
}

extend google.protobuf.EnumValueOptions {
	optional string enumvalue_customname = 66001;
}

extend google.protobuf.FileOptions {
	optional bool goproto_getters_all = 63001;
	optional bool goproto_enum_prefix_all = 63002;
	optional bool goproto_stringer_all = 63003;
	optional bool verbose_equal_all = 63004;
	optional bool face_all = 63005;
	optional bool gostring_all = 63006;
	optional bool populate_all = 63007;
	optional bool stringer_all = 63008;
	optional bool onlyone_all = 63009;

	optional bool equal_all = 63013;
	optional bool description_all = 63014;
	optional bool testgen_all = 63015;
	optional bool benchgen_all = 63016;
	optional bool marshaler_all = 63017;
	optional bool unmarshaler_all = 63018;
	optional bool stable_marshaler_all = 63019;

	optional bool sizer_all = 63020;

	optional bool goproto_enum_stringer_all = 63021;
	optional bool enum_stringer_all = 63022;

	optional bool unsafe_marshaler_all = 63023;
	optional bool unsafe_unmarshaler_all = 63024;

	optional bool goproto_extensions_map_all = 63025;
	optional bool goproto_unrecognized_all = 63026;
	optional bool gogoproto_import = 63027;
	optional bool protosizer_all = 63028;
	optional bool compare_all = 63029;
    optional bool typedecl_all = 63030;
    optional bool enumdecl_all = 63031;

	optional bool goproto_registration = 63032;
	optional bool messagename_all = 63033;
}

extend google.protobuf.MessageOptions {
	optional bool goproto_getters = 64001;
	optional bool goproto_stringer = 64003;
	optional bool verbose_equal = 64004;
	optional bool face = 64005;
	optional bool gostring = 64006;
	optional bool populate = 64007;
	optional bool stringer = 67008;
	optional bool onlyone = 64009;

	optional bool equal = 64013;
	optional bool description = 64014;
	optional bool testgen = 64015;
	optional bool benchgen = 64016;
	optional bool marshaler = 64017;
	optional bool unmarshaler = 64018;
	optional bool stable_marshaler = 64019;

	optional bool sizer = 64020;

	optional bool unsafe_marshaler = 64023;
	optional bool unsafe_unmarshaler = 64024;

	optional bool goproto_extensions_map = 64025;
	optional bool goproto_unrecognized = 64026;

	optional bool protosizer = 64028;
	optional bool compare = 64029;

	optional bool typedecl = 64030;

	optional bool messagename = 64033;
}

extend google.protobuf.FieldOptions {
	optional bool nullable = 65001;
	optional bool embed = 65002;
	optional string customtype = 65003;
	optional string customname = 65004;
	optional string jsontag = 65005;
	optional string moretags = 65006;
	optional string casttype = 65007;
	optional string castkey = 65008;
	optional string castvalue = 65009;

	optional bool stdtime = 65010;
	optional bool stdduration = 65011;
}
